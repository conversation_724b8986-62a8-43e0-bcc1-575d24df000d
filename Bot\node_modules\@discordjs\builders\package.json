{"$schema": "https://json.schemastore.org/package.json", "name": "@discordjs/builders", "version": "1.10.1", "description": "A set of builders that you can use when creating your bot", "exports": {".": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "directories": {"lib": "src", "test": "__tests__"}, "files": ["dist"], "contributors": ["<PERSON> <<EMAIL>>", "Crawl <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "SpaceEEC <<EMAIL>>", "<PERSON><PERSON> Rom<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "keywords": ["discord", "api", "bot", "client", "node", "discordapp", "discordjs"], "repository": {"type": "git", "url": "https://github.com/discordjs/discord.js.git", "directory": "packages/builders"}, "bugs": {"url": "https://github.com/discordjs/discord.js/issues"}, "homepage": "https://discord.js.org", "funding": "https://github.com/discordjs/discord.js?sponsor", "dependencies": {"@sapphire/shapeshift": "^4.0.0", "discord-api-types": "^0.37.119", "fast-deep-equal": "^3.1.3", "ts-mixer": "^6.0.4", "tslib": "^2.6.3", "@discordjs/util": "^1.1.1", "@discordjs/formatters": "^0.6.0"}, "devDependencies": {"@favware/cliff-jumper": "^4.1.0", "@types/node": "^16.18.105", "@vitest/coverage-v8": "^2.0.5", "cross-env": "^7.0.3", "esbuild-plugin-version-injector": "^1.2.1", "eslint": "^8.57.0", "eslint-config-neon": "^0.1.62", "eslint-formatter-pretty": "^6.0.1", "prettier": "^3.3.3", "tsup": "^8.2.4", "turbo": "^2.0.14", "typescript": "~5.5.4", "vitest": "^2.0.5", "@discordjs/api-extractor": "^7.38.1", "@discordjs/scripts": "^0.1.0"}, "engines": {"node": ">=16.11.0"}, "publishConfig": {"access": "public", "provenance": true}, "scripts": {"test": "vitest run", "build": "tsc --noEmit && tsup", "build:docs": "tsc -p tsconfig.docs.json", "lint": "prettier --check . && cross-env TIMING=1 eslint --format=pretty src __tests__", "format": "prettier --write . && cross-env TIMING=1 eslint --fix --format=pretty src __tests__", "fmt": "pnpm run format", "docs": "pnpm run build:docs && api-extractor run --local --minify && generate-split-documentation", "changelog": "git cliff --prepend ./CHANGELOG.md -u -c ./cliff.toml -r ../../ --include-path 'packages/builders/*'", "release": "cliff-jumper"}}