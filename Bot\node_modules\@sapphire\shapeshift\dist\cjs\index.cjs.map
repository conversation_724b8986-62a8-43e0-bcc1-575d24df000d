{"version": 3, "sources": ["../../src/constraints/ObjectConstrains.ts", "../../node_modules/@jspm/core/nodelibs/browser/chunk-5decc758.js", "../../node_modules/@jspm/core/nodelibs/browser/chunk-b4205b57.js", "../../node_modules/@jspm/core/nodelibs/browser/chunk-ce0fbc82.js", "node-modules-polyfills:util", "../../src/lib/errors/BaseError.ts", "../../src/lib/errors/BaseConstraintError.ts", "../../src/lib/errors/ExpectedConstraintError.ts", "../../src/lib/Result.ts", "../../src/lib/configs.ts", "../../src/validators/util/getValue.ts", "../../src/validators/BaseValidator.ts", "../../src/constraints/util/isUnique.ts", "../../src/constraints/util/operators.ts", "../../src/constraints/ArrayConstraints.ts", "../../src/lib/errors/CombinedPropertyError.ts", "../../src/lib/errors/ValidationError.ts", "../../src/validators/ArrayValidator.ts", "../../src/constraints/BigIntConstraints.ts", "../../src/validators/BigIntValidator.ts", "../../src/constraints/BooleanConstraints.ts", "../../src/validators/BooleanValidator.ts", "../../src/constraints/DateConstraints.ts", "../../src/validators/DateValidator.ts", "../../src/lib/errors/ExpectedValidationError.ts", "../../src/validators/InstanceValidator.ts", "../../src/validators/LiteralValidator.ts", "../../src/validators/NeverValidator.ts", "../../src/validators/NullishValidator.ts", "../../src/constraints/NumberConstraints.ts", "../../src/validators/NumberValidator.ts", "../../src/lib/errors/MissingPropertyError.ts", "../../src/lib/errors/UnknownPropertyError.ts", "../../src/validators/DefaultValidator.ts", "../../src/lib/errors/CombinedError.ts", "../../src/validators/UnionValidator.ts", "../../src/validators/ObjectValidator.ts", "../../src/validators/PassthroughValidator.ts", "../../src/validators/RecordValidator.ts", "../../src/validators/SetValidator.ts", "../../src/constraints/util/emailValidator.ts", "../../src/constraints/util/net.ts", "../../src/constraints/util/phoneValidator.ts", "../../src/lib/errors/MultiplePossibilitiesConstraintError.ts", "../../src/constraints/util/common/combinedResultFn.ts", "../../src/constraints/util/urlValidators.ts", "../../src/constraints/StringConstraints.ts", "../../src/validators/StringValidator.ts", "../../src/validators/TupleValidator.ts", "../../src/validators/MapValidator.ts", "../../src/validators/LazyValidator.ts", "../../src/lib/errors/UnknownEnumValueError.ts", "../../src/validators/NativeEnumValidator.ts", "../../src/constraints/TypedArrayLengthConstraints.ts", "../../src/constraints/util/common/vowels.ts", "../../src/constraints/util/typedArray.ts", "../../src/validators/TypedArrayValidator.ts", "../../src/lib/Shapes.ts", "../../src/index.ts"], "names": ["e", "n", "t", "o", "r", "l", "c", "u", "i", "f", "a", "s", "p", "d", "m", "h", "T", "_extend", "callbackify", "debuglog", "deprecate", "format", "inherits", "inspect", "isArray", "isBoolean", "<PERSON><PERSON><PERSON><PERSON>", "isDate", "isError", "isFunction", "isNull", "isNullOrUndefined", "isNumber", "isObject", "isPrimitive", "isRegExp", "isString", "isSymbol", "isUndefined", "log", "promisify", "types", "TextEncoder", "TextDecoder", "k", "v", "uniqueArray", "b", "value", "x"], "mappings": ";;;;AAAA,OAAO,SAAS;;;ACAhB,IAAI;AAAJ,IAAM;AAAN,IAAQ;AAAR,IAAU,IAAE,eAAa,OAAO,aAAW,aAAW,eAAa,OAAO,OAAK,OAAK;AAApF,IAA2F,IAAE,IAAE,CAAC;AAAE,SAAS,IAAG;AAAC,QAAM,IAAI,MAAM,iCAAiC;AAAC;AAAtD;AAAuD,SAAS,IAAG;AAAC,QAAM,IAAI,MAAM,mCAAmC;AAAC;AAAxD;AAAyD,SAAS,EAAEA,IAAE;AAAC,MAAG,MAAI;AAAW,WAAO,WAAWA,IAAE,CAAC;AAAE,OAAI,MAAI,KAAG,CAAC,MAAI;AAAW,WAAO,IAAE,YAAW,WAAWA,IAAE,CAAC;AAAE,MAAG;AAAC,WAAO,EAAEA,IAAE,CAAC;AAAA,EAAC,SAAOC,IAAE;AAAC,QAAG;AAAC,aAAO,EAAE,KAAK,MAAKD,IAAE,CAAC;AAAA,IAAC,SAAOC,IAAE;AAAC,aAAO,EAAE,KAAK,QAAM,GAAED,IAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAzM;AAA0M,CAAC,WAAU;AAAC,MAAG;AAAC,QAAE,cAAY,OAAO,aAAW,aAAW;AAAA,EAAE,SAAOA,IAAE;AAAC,QAAE;AAAA,EAAE;AAAC,MAAG;AAAC,QAAE,cAAY,OAAO,eAAa,eAAa;AAAA,EAAE,SAAOA,IAAE;AAAC,QAAE;AAAA,EAAE;AAAC,EAAE;AAAE,IAAI;AAAJ,IAAM,IAAE,CAAC;AAAT,IAAW,IAAE;AAAb,IAAgB,IAAE;AAAG,SAAS,IAAG;AAAC,OAAG,MAAI,IAAE,OAAG,EAAE,SAAO,IAAE,EAAE,OAAO,CAAC,IAAE,IAAE,IAAG,EAAE,UAAQ,EAAE;AAAG;AAA3D;AAA4D,SAAS,IAAG;AAAC,MAAG,CAAC,GAAE;AAAC,QAAIA,KAAE,EAAE,CAAC;AAAE,QAAE;AAAG,aAAQE,KAAE,EAAE,QAAOA,MAAG;AAAC,WAAI,IAAE,GAAE,IAAE,CAAC,GAAE,EAAE,IAAEA;AAAG,aAAG,EAAE,CAAC,EAAE,IAAI;AAAE,UAAE,IAAGA,KAAE,EAAE;AAAA,IAAO;AAAC,QAAE,MAAK,IAAE,OAAG,SAASF,IAAE;AAAC,UAAG,MAAI;AAAa,eAAO,aAAaA,EAAC;AAAE,WAAI,MAAI,KAAG,CAAC,MAAI;AAAa,eAAO,IAAE,cAAa,aAAaA,EAAC;AAAE,UAAG;AAAC,UAAEA,EAAC;AAAA,MAAE,SAAOE,IAAE;AAAC,YAAG;AAAC,iBAAO,EAAE,KAAK,MAAKF,EAAC;AAAA,QAAC,SAAOE,IAAE;AAAC,iBAAO,EAAE,KAAK,QAAM,GAAEF,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,EAAEA,EAAC;AAAA,EAAE;AAAC;AAAjU;AAAkU,SAAS,EAAEA,IAAEE,IAAE;AAAC,GAAC,QAAM,GAAG,MAAIF,KAAG,QAAM,GAAG,QAAME;AAAE;AAAzC;AAA0C,SAAS,IAAG;AAAC;AAAJ;AAAK,EAAE,WAAS,SAASF,IAAE;AAAC,MAAIE,KAAE,IAAI,MAAM,UAAU,SAAO,CAAC;AAAE,MAAG,UAAU,SAAO;AAAE,aAAQD,KAAE,GAAEA,KAAE,UAAU,QAAOA;AAAI,MAAAC,GAAED,KAAE,CAAC,IAAE,UAAUA,EAAC;AAAE,IAAE,KAAK,IAAI,EAAED,IAAEE,EAAC,CAAC,GAAE,MAAI,EAAE,UAAQ,KAAG,EAAE,CAAC;AAAE,GAAE,EAAE,UAAU,MAAI,WAAU;AAAC,GAAC,QAAM,GAAG,IAAI,MAAM,OAAM,QAAM,GAAG,KAAK;AAAE,GAAE,EAAE,QAAM,WAAU,EAAE,UAAQ,MAAG,EAAE,MAAI,CAAC,GAAE,EAAE,OAAK,CAAC,GAAE,EAAE,UAAQ,IAAG,EAAE,WAAS,CAAC,GAAE,EAAE,KAAG,GAAE,EAAE,cAAY,GAAE,EAAE,OAAK,GAAE,EAAE,MAAI,GAAE,EAAE,iBAAe,GAAE,EAAE,qBAAmB,GAAE,EAAE,OAAK,GAAE,EAAE,kBAAgB,GAAE,EAAE,sBAAoB,GAAE,EAAE,YAAU,SAASF,IAAE;AAAC,SAAO,CAAC;AAAC,GAAE,EAAE,UAAQ,SAASA,IAAE;AAAC,QAAM,IAAI,MAAM,kCAAkC;AAAC,GAAE,EAAE,MAAI,WAAU;AAAC,SAAO;AAAG,GAAE,EAAE,QAAM,SAASA,IAAE;AAAC,QAAM,IAAI,MAAM,gCAAgC;AAAC,GAAE,EAAE,QAAM,WAAU;AAAC,SAAO;AAAC;AAAE,IAAI,IAAE;AAAE,EAAE;AAAY,EAAE;AAAK,EAAE;AAAQ,EAAE;AAAQ,EAAE;AAAM,EAAE;AAAI,EAAE;AAAK,EAAE;AAAI,EAAE;AAAU,EAAE;AAAS,EAAE;AAAI,EAAE;AAAG,EAAE;AAAK,EAAE;AAAgB,EAAE;AAAoB,EAAE;AAAmB,EAAE;AAAe,EAAE;AAAM,EAAE;AAAM,EAAE;AAAQ,EAAE;;;ACE78D,IAAIE,KAAE,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO;AAAzD,IAAqEF,KAAE,OAAO,UAAU;AAAxF,IAAiGG,KAAE,gCAASA,IAAE;AAAC,SAAO,EAAED,MAAGC,MAAG,YAAU,OAAOA,MAAG,OAAO,eAAeA,OAAI,yBAAuBH,GAAE,KAAKG,EAAC;AAAC,GAAzG;AAAnG,IAA8MF,KAAE,gCAASC,IAAE;AAAC,SAAO,CAAC,CAACC,GAAED,EAAC,KAAG,SAAOA,MAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,GAAE,UAAQA,GAAE,UAAQ,KAAG,qBAAmBF,GAAE,KAAKE,EAAC,KAAG,wBAAsBF,GAAE,KAAKE,GAAE,MAAM;AAAC,GAArK;AAAhN,IAAuXE,KAAE,WAAU;AAAC,SAAOD,GAAE,SAAS;AAAC,EAAE;AAAEA,GAAE,oBAAkBF;AAAE,IAAII,KAAED,KAAED,KAAEF;AAAE,IAAI,MAAI,OAAO,UAAU;AAAzB,IAAkC,MAAI,SAAS,UAAU;AAAzD,IAAkE,MAAI;AAAtE,IAA4F,MAAI,cAAY,OAAO,UAAQ,YAAU,OAAO,OAAO;AAAnJ,IAA+J,MAAI,OAAO;AAA1K,IAAyLK,KAAE,WAAU;AAAC,MAAG,CAAC;AAAI,WAAO;AAAG,MAAG;AAAC,WAAO,SAAS,uBAAuB,EAAE;AAAA,EAAC,SAAOJ,IAAE;AAAA,EAAC;AAAC,EAAE;AAAnR,IAAqRK,KAAED,KAAE,IAAIA,EAAC,IAAE,CAAC;AAAjS,IAAmSE,KAAE,gCAASF,IAAE;AAAC,SAAO,cAAY,OAAOA,OAAI,CAAC,CAAC,IAAI,KAAK,IAAI,KAAKA,EAAC,CAAC,MAAI,MAAI,IAAIA,EAAC,MAAIC,KAAE,iCAA+B,IAAI,KAAKD,EAAC;AAAG,GAA/H;AAAiI,IAAI,MAAI,cAAY,OAAO,OAAO,SAAO,SAASJ,IAAEF,IAAE;AAAC,EAAAA,OAAIE,GAAE,SAAOF,IAAEE,GAAE,YAAU,OAAO,OAAOF,GAAE,WAAU,EAAC,aAAY,EAAC,OAAME,IAAE,YAAW,OAAG,UAAS,MAAG,cAAa,KAAE,EAAC,CAAC;AAAG,IAAE,SAASA,IAAEF,IAAE;AAAC,MAAGA,IAAE;AAAC,IAAAE,GAAE,SAAOF;AAAE,QAAIG,KAAE,kCAAU;AAAA,IAAC,GAAX;AAAa,IAAAA,GAAE,YAAUH,GAAE,WAAUE,GAAE,YAAU,IAAIC,MAAED,GAAE,UAAU,cAAYA;AAAA,EAAE;AAAC;AAAE,IAAI,MAAI,gCAASF,IAAE;AAAC,SAAOA,MAAG,YAAU,OAAOA,MAAG,cAAY,OAAOA,GAAE,QAAM,cAAY,OAAOA,GAAE,QAAM,cAAY,OAAOA,GAAE;AAAS,GAA9H;AAAR,IAAwI,MAAI,CAAC;AAA7I,IAA+I,MAAI;AAAnJ,IAAuJS,KAAEJ;AAAzJ,IAA2JK,KAAEF;AAAE,SAAS,IAAIR,IAAE;AAAC,SAAOA,GAAE,KAAK,KAAKA,EAAC;AAAC;AAA5B;AAA6B,IAAIW,KAAE,eAAa,OAAO;AAA1B,IAAiCC,KAAE,eAAa,OAAO;AAAvD,IAA8D,IAAEA,MAAG,WAAS,OAAO;AAAnF,IAA+F,MAAI,eAAa,OAAO;AAAvH,IAAkIC,KAAE,eAAa,OAAO;AAAY,IAAG,OAAK;AAAE,MAAI,IAAE,OAAO,eAAe,WAAW,SAAS,GAAE,IAAE,IAAI,OAAO,yBAAyB,GAAE,OAAO,WAAW,EAAE,GAAG;AAAE,IAAIC,KAAE,IAAI,OAAO,UAAU,QAAQ;AAAnC,IAAqCC,KAAE,IAAI,OAAO,UAAU,OAAO;AAAnE,IAAqE,IAAE,IAAI,OAAO,UAAU,OAAO;AAAnG,IAAqG,IAAE,IAAI,QAAQ,UAAU,OAAO;AAAE,IAAGJ;AAAE,MAAI,IAAE,IAAI,OAAO,UAAU,OAAO;AAAE,IAAGC;AAAE,MAAI,IAAE,IAAI,OAAO,UAAU,OAAO;AAAE,SAAS,EAAEZ,IAAEE,IAAE;AAAC,MAAG,YAAU,OAAOF;AAAE,WAAO;AAAG,MAAG;AAAC,WAAOE,GAAEF,EAAC,GAAE;AAAA,EAAE,SAAOA,IAAE;AAAC,WAAO;AAAA,EAAE;AAAC;AAA7E;AAA8E,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,WAAS,EAAEA,EAAC,IAAE,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC;AAAC;AAAjG;AAAkG,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,iBAAe,EAAEA,EAAC,IAAE,0BAAwBc,GAAEd,EAAC,KAAG,IAAIA,EAAC,KAAG,WAASA,GAAE;AAAM;AAA9F;AAA+F,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,wBAAsB,EAAEA,EAAC,IAAE,iCAA+Bc,GAAEd,EAAC;AAAC;AAAjF;AAAkF,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,kBAAgB,EAAEA,EAAC,IAAE,2BAAyBc,GAAEd,EAAC;AAAC;AAArE;AAAsE,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,kBAAgB,EAAEA,EAAC,IAAE,2BAAyBc,GAAEd,EAAC;AAAC;AAArE;AAAsE,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,gBAAc,EAAEA,EAAC,IAAE,yBAAuBc,GAAEd,EAAC;AAAC;AAAjE;AAAkE,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,iBAAe,EAAEA,EAAC,IAAE,0BAAwBc,GAAEd,EAAC;AAAC;AAAnE;AAAoE,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,iBAAe,EAAEA,EAAC,IAAE,0BAAwBc,GAAEd,EAAC;AAAC;AAAnE;AAAoE,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,mBAAiB,EAAEA,EAAC,IAAE,4BAA0Bc,GAAEd,EAAC;AAAC;AAAvE;AAAwE,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,mBAAiB,EAAEA,EAAC,IAAE,4BAA0Bc,GAAEd,EAAC;AAAC;AAAvE;AAAwE,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,oBAAkB,EAAEA,EAAC,IAAE,6BAA2Bc,GAAEd,EAAC;AAAC;AAAzE;AAA0E,SAAS,EAAEA,IAAE;AAAC,SAAO,OAAK,IAAE,qBAAmB,EAAEA,EAAC,IAAE,8BAA4Bc,GAAEd,EAAC;AAAC;AAA3E;AAA4E,SAASgB,GAAEhB,IAAE;AAAC,SAAO,mBAAiBc,GAAEd,EAAC;AAAC;AAAjC,OAAAgB,IAAA;AAAkC,SAAS,EAAEhB,IAAE;AAAC,SAAO,mBAAiBc,GAAEd,EAAC;AAAC;AAAjC;AAAkC,SAAS,EAAEA,IAAE;AAAC,SAAO,uBAAqBc,GAAEd,EAAC;AAAC;AAArC;AAAsC,SAAS,EAAEA,IAAE;AAAC,SAAO,uBAAqBc,GAAEd,EAAC;AAAC;AAArC;AAAsC,SAAS,EAAEA,IAAE;AAAC,SAAO,2BAAyBc,GAAEd,EAAC;AAAC;AAAzC;AAA0C,SAAS,EAAEA,IAAE;AAAC,SAAO,eAAa,OAAO,gBAAc,EAAE,UAAQ,EAAEA,EAAC,IAAEA,cAAa;AAAY;AAAtF;AAAuF,SAAS,EAAEA,IAAE;AAAC,SAAO,wBAAsBc,GAAEd,EAAC;AAAC;AAAtC;AAAuC,SAAS,EAAEA,IAAE;AAAC,SAAO,eAAa,OAAO,aAAW,EAAE,UAAQ,EAAEA,EAAC,IAAEA,cAAa;AAAS;AAAhF;AAAiF,SAAS,EAAEA,IAAE;AAAC,SAAO,iCAA+Bc,GAAEd,EAAC;AAAC;AAA/C;AAAgD,SAAS,EAAEA,IAAE;AAAC,SAAO,eAAa,OAAO,sBAAoB,EAAE,UAAQ,EAAEA,EAAC,IAAEA,cAAa;AAAkB;AAAlG;AAAmG,SAAS,EAAEA,IAAE;AAAC,SAAO,EAAEA,IAAEe,EAAC;AAAC;AAAlB;AAAmB,SAAS,EAAEf,IAAE;AAAC,SAAO,EAAEA,IAAE,CAAC;AAAC;AAAlB;AAAmB,SAAS,EAAEA,IAAE;AAAC,SAAO,EAAEA,IAAE,CAAC;AAAC;AAAlB;AAAmB,SAAS,EAAEA,IAAE;AAAC,SAAOW,MAAG,EAAEX,IAAE,CAAC;AAAC;AAArB;AAAsB,SAAS,EAAEA,IAAE;AAAC,SAAOY,MAAG,EAAEZ,IAAE,CAAC;AAAC;AAArB;AAAsB,IAAI,oBAAkBS,IAAE,IAAI,sBAAoBC,IAAE,IAAI,YAAU,SAASV,IAAE;AAAC,SAAO,eAAa,OAAO,WAASA,cAAa,WAAS,SAAOA,MAAG,YAAU,OAAOA,MAAG,cAAY,OAAOA,GAAE,QAAM,cAAY,OAAOA,GAAE;AAAK,GAAE,IAAI,oBAAkB,SAASA,IAAE;AAAC,SAAOa,MAAG,YAAY,SAAO,YAAY,OAAOb,EAAC,IAAE,EAAEA,EAAC,KAAG,EAAEA,EAAC;AAAC,GAAE,IAAI,eAAa,GAAE,IAAI,eAAa,GAAE,IAAI,sBAAoB,GAAE,IAAI,gBAAc,GAAE,IAAI,gBAAc,GAAE,IAAI,cAAY,GAAE,IAAI,eAAa,GAAE,IAAI,eAAa,GAAE,IAAI,iBAAe,GAAE,IAAI,iBAAe,GAAE,IAAI,kBAAgB,GAAE,IAAI,mBAAiB,GAAEgB,GAAE,UAAQ,eAAa,OAAO,OAAKA,GAAE,oBAAI,KAAG,GAAE,IAAI,QAAM,SAAShB,IAAE;AAAC,SAAO,eAAa,OAAO,QAAMgB,GAAE,UAAQA,GAAEhB,EAAC,IAAEA,cAAa;AAAI,GAAE,EAAE,UAAQ,eAAa,OAAO,OAAK,EAAE,oBAAI,KAAG,GAAE,IAAI,QAAM,SAASA,IAAE;AAAC,SAAO,eAAa,OAAO,QAAM,EAAE,UAAQ,EAAEA,EAAC,IAAEA,cAAa;AAAI,GAAE,EAAE,UAAQ,eAAa,OAAO,WAAS,EAAE,oBAAI,SAAO,GAAE,IAAI,YAAU,SAASA,IAAE;AAAC,SAAO,eAAa,OAAO,YAAU,EAAE,UAAQ,EAAEA,EAAC,IAAEA,cAAa;AAAQ,GAAE,EAAE,UAAQ,eAAa,OAAO,WAAS,EAAE,oBAAI,SAAO,GAAE,IAAI,YAAU,SAASA,IAAE;AAAC,SAAO,EAAEA,EAAC;AAAC,GAAE,EAAE,UAAQ,eAAa,OAAO,eAAa,EAAE,IAAI,aAAW,GAAE,IAAI,gBAAc,GAAE,EAAE,UAAQ,eAAa,OAAO,eAAa,eAAa,OAAO,YAAU,EAAE,IAAI,SAAS,IAAI,YAAY,CAAC,GAAE,GAAE,CAAC,CAAC,GAAE,IAAI,aAAW,GAAE,EAAE,UAAQ,eAAa,OAAO,qBAAmB,EAAE,IAAI,mBAAiB,GAAE,IAAI,sBAAoB,GAAE,IAAI,kBAAgB,SAASA,IAAE;AAAC,SAAO,6BAA2Bc,GAAEd,EAAC;AAAC,GAAE,IAAI,gBAAc,SAASA,IAAE;AAAC,SAAO,4BAA0Bc,GAAEd,EAAC;AAAC,GAAE,IAAI,gBAAc,SAASA,IAAE;AAAC,SAAO,4BAA0Bc,GAAEd,EAAC;AAAC,GAAE,IAAI,oBAAkB,SAASA,IAAE;AAAC,SAAO,yBAAuBc,GAAEd,EAAC;AAAC,GAAE,IAAI,8BAA4B,SAASA,IAAE;AAAC,SAAO,kCAAgCc,GAAEd,EAAC;AAAC,GAAE,IAAI,iBAAe,GAAE,IAAI,iBAAe,GAAE,IAAI,kBAAgB,GAAE,IAAI,iBAAe,GAAE,IAAI,iBAAe,GAAE,IAAI,mBAAiB,SAASA,IAAE;AAAC,SAAO,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC,KAAG,EAAEA,EAAC;AAAC,GAAE,IAAI,mBAAiB,SAASA,IAAE;AAAC,SAAO,QAAM,EAAEA,EAAC,KAAG,EAAEA,EAAC;AAAE,GAAE,CAAC,WAAU,cAAa,yBAAyB,EAAE,QAAS,SAASA,IAAE;AAAC,SAAO,eAAe,KAAIA,IAAE,EAAC,YAAW,OAAG,OAAM,WAAU;AAAC,UAAM,IAAI,MAAMA,KAAE,+BAA+B;AAAA,EAAC,EAAC,CAAC;AAAE,CAAE;AAAE,IAAI,IAAE,eAAa,OAAO,aAAW,aAAW,eAAa,OAAO,OAAK,OAAK;AAA9E,IAAqF,IAAE,CAAC;AAAxF,IAA0F,IAAE;AAA5F,IAAgG,KAAG,OAAO,6BAA2B,SAASA,IAAE;AAAC,WAAQE,KAAE,OAAO,KAAKF,EAAC,GAAEI,KAAE,CAAC,GAAEH,KAAE,GAAEA,KAAEC,GAAE,QAAOD;AAAI,IAAAG,GAAEF,GAAED,EAAC,CAAC,IAAE,OAAO,yBAAyBD,IAAEE,GAAED,EAAC,CAAC;AAAE,SAAOG;AAAC;AAA1P,IAA4P,KAAG;AAAW,EAAE,SAAO,SAASJ,IAAE;AAAC,MAAG,CAAC,GAAGA,EAAC,GAAE;AAAC,aAAQE,KAAE,CAAC,GAAEE,KAAE,GAAEA,KAAE,UAAU,QAAOA;AAAI,MAAAF,GAAE,KAAK,GAAG,UAAUE,EAAC,CAAC,CAAC;AAAE,WAAOF,GAAE,KAAK,GAAG;AAAA,EAAC;AAAC,EAAAE,KAAE;AAAE,WAAQH,KAAE,WAAUO,KAAEP,GAAE,QAAOE,KAAE,OAAOH,EAAC,EAAE,QAAQ,IAAI,SAASA,IAAE;AAAC,QAAG,SAAOA;AAAE,aAAO;AAAI,QAAGI,MAAGI;AAAE,aAAOR;AAAE,YAAOA,IAAE;AAAA,MAAC,KAAI;AAAK,eAAO,OAAOC,GAAEG,IAAG,CAAC;AAAA,MAAE,KAAI;AAAK,eAAO,OAAOH,GAAEG,IAAG,CAAC;AAAA,MAAE,KAAI;AAAK,YAAG;AAAC,iBAAO,KAAK,UAAUH,GAAEG,IAAG,CAAC;AAAA,QAAC,SAAOJ,IAAE;AAAC,iBAAO;AAAA,QAAY;AAAA,MAAC;AAAQ,eAAOA;AAAA,IAAC;AAAA,EAAC,CAAE,GAAEO,KAAEN,GAAEG,EAAC,GAAEA,KAAEI,IAAED,KAAEN,GAAE,EAAEG,EAAC;AAAE,OAAGG,EAAC,KAAG,CAAC,GAAGA,EAAC,IAAEJ,MAAG,MAAII,KAAEJ,MAAG,MAAI,GAAGI,EAAC;AAAE,SAAOJ;AAAC,GAAE,EAAE,YAAU,SAASH,IAAEE,IAAE;AAAC,MAAG,WAAS,KAAG,SAAK,EAAE;AAAc,WAAOF;AAAE,MAAG,WAAS;AAAE,WAAO,WAAU;AAAC,aAAO,EAAE,UAAUA,IAAEE,EAAC,EAAE,MAAM,QAAM,GAAE,SAAS;AAAA,IAAC;AAAE,MAAIE,KAAE;AAAG,SAAO,WAAU;AAAC,QAAG,CAACA,IAAE;AAAC,UAAG,EAAE;AAAiB,cAAM,IAAI,MAAMF,EAAC;AAAE,QAAE,mBAAiB,QAAQ,MAAMA,EAAC,IAAE,QAAQ,MAAMA,EAAC,GAAEE,KAAE;AAAA,IAAG;AAAC,WAAOJ,GAAE,MAAM,QAAM,GAAE,SAAS;AAAA,EAAC;AAAC;AAAE,IAAI,KAAG,CAAC;AAAR,IAAU,KAAG;AAAK,IAAG,EAAE,IAAI,YAAW;AAAK,OAAG,EAAE,IAAI;AAAW,OAAG,GAAG,QAAQ,sBAAqB,MAAM,EAAE,QAAQ,OAAM,IAAI,EAAE,QAAQ,MAAK,KAAK,EAAE,YAAY,GAAE,KAAG,IAAI,OAAO,MAAI,KAAG,KAAI,GAAG;AAAE;AAAnJ;AAAoJ,SAAS,GAAGA,IAAEE,IAAE;AAAC,MAAIE,KAAE,EAAC,MAAK,CAAC,GAAE,SAAQ,GAAE;AAAE,SAAO,UAAU,UAAQ,MAAIA,GAAE,QAAM,UAAU,CAAC,IAAG,UAAU,UAAQ,MAAIA,GAAE,SAAO,UAAU,CAAC,IAAG,GAAGF,EAAC,IAAEE,GAAE,aAAWF,KAAEA,MAAG,EAAE,QAAQE,IAAEF,EAAC,GAAE,GAAGE,GAAE,UAAU,MAAIA,GAAE,aAAW,QAAI,GAAGA,GAAE,KAAK,MAAIA,GAAE,QAAM,IAAG,GAAGA,GAAE,MAAM,MAAIA,GAAE,SAAO,QAAI,GAAGA,GAAE,aAAa,MAAIA,GAAE,gBAAc,OAAIA,GAAE,WAASA,GAAE,UAAQ,KAAI,GAAGA,IAAEJ,IAAEI,GAAE,KAAK;AAAC;AAArV;AAAsV,SAAS,GAAGJ,IAAEE,IAAE;AAAC,MAAIE,KAAE,GAAG,OAAOF,EAAC;AAAE,SAAOE,KAAE,UAAK,GAAG,OAAOA,EAAC,EAAE,CAAC,IAAE,MAAIJ,KAAE,UAAK,GAAG,OAAOI,EAAC,EAAE,CAAC,IAAE,MAAIJ;AAAC;AAAzF;AAA0F,SAAS,GAAGA,IAAEE,IAAE;AAAC,SAAOF;AAAC;AAAhB;AAAiB,SAAS,GAAGA,IAAEE,IAAEE,IAAE;AAAC,MAAGJ,GAAE,iBAAeE,MAAG,GAAGA,GAAE,OAAO,KAAGA,GAAE,YAAU,EAAE,YAAU,CAACA,GAAE,eAAaA,GAAE,YAAY,cAAYA,KAAG;AAAC,QAAID,KAAEC,GAAE,QAAQE,IAAEJ,EAAC;AAAE,WAAO,GAAGC,EAAC,MAAIA,KAAE,GAAGD,IAAEC,IAAEG,EAAC,IAAGH;AAAA,EAAC;AAAC,MAAIO,KAAE,SAASR,IAAEE,IAAE;AAAC,QAAG,GAAGA,EAAC;AAAE,aAAOF,GAAE,QAAQ,aAAY,WAAW;AAAE,QAAG,GAAGE,EAAC,GAAE;AAAC,UAAIE,KAAE,MAAI,KAAK,UAAUF,EAAC,EAAE,QAAQ,UAAS,EAAE,EAAE,QAAQ,MAAK,KAAK,EAAE,QAAQ,QAAO,GAAG,IAAE;AAAI,aAAOF,GAAE,QAAQI,IAAE,QAAQ;AAAA,IAAC;AAAC,QAAG,GAAGF,EAAC;AAAE,aAAOF,GAAE,QAAQ,KAAGE,IAAE,QAAQ;AAAE,QAAG,GAAGA,EAAC;AAAE,aAAOF,GAAE,QAAQ,KAAGE,IAAE,SAAS;AAAE,QAAG,GAAGA,EAAC;AAAE,aAAOF,GAAE,QAAQ,QAAO,MAAM;AAAA,EAAC,EAAEA,IAAEE,EAAC;AAAE,MAAGM;AAAE,WAAOA;AAAE,MAAIL,KAAE,OAAO,KAAKD,EAAC,GAAEK,KAAE,SAASP,IAAE;AAAC,QAAIE,KAAE,CAAC;AAAE,WAAOF,GAAE,QAAS,SAASA,IAAEI,IAAE;AAAC,MAAAF,GAAEF,EAAC,IAAE;AAAA,IAAG,CAAE,GAAEE;AAAA,EAAC,EAAEC,EAAC;AAAE,MAAGH,GAAE,eAAaG,KAAE,OAAO,oBAAoBD,EAAC,IAAG,GAAGA,EAAC,MAAIC,GAAE,QAAQ,SAAS,KAAG,KAAGA,GAAE,QAAQ,aAAa,KAAG;AAAG,WAAO,GAAGD,EAAC;AAAE,MAAG,MAAIC,GAAE,QAAO;AAAC,QAAG,GAAGD,EAAC,GAAE;AAAC,UAAIO,KAAEP,GAAE,OAAK,OAAKA,GAAE,OAAK;AAAG,aAAOF,GAAE,QAAQ,cAAYS,KAAE,KAAI,SAAS;AAAA,IAAC;AAAC,QAAG,GAAGP,EAAC;AAAE,aAAOF,GAAE,QAAQ,OAAO,UAAU,SAAS,KAAKE,EAAC,GAAE,QAAQ;AAAE,QAAG,GAAGA,EAAC;AAAE,aAAOF,GAAE,QAAQ,KAAK,UAAU,SAAS,KAAKE,EAAC,GAAE,MAAM;AAAE,QAAG,GAAGA,EAAC;AAAE,aAAO,GAAGA,EAAC;AAAA,EAAC;AAAC,MAAIQ,IAAEJ,KAAE,IAAGK,KAAE,OAAGC,KAAE,CAAC,KAAI,GAAG;AAAE,GAAC,GAAGV,EAAC,MAAIS,KAAE,MAAGC,KAAE,CAAC,KAAI,GAAG,IAAG,GAAGV,EAAC,OAAKI,KAAE,gBAAcJ,GAAE,OAAK,OAAKA,GAAE,OAAK,MAAI;AAAK,SAAO,GAAGA,EAAC,MAAII,KAAE,MAAI,OAAO,UAAU,SAAS,KAAKJ,EAAC,IAAG,GAAGA,EAAC,MAAII,KAAE,MAAI,KAAK,UAAU,YAAY,KAAKJ,EAAC,IAAG,GAAGA,EAAC,MAAII,KAAE,MAAI,GAAGJ,EAAC,IAAG,MAAIC,GAAE,UAAQQ,MAAG,KAAGT,GAAE,SAAOE,KAAE,IAAE,GAAGF,EAAC,IAAEF,GAAE,QAAQ,OAAO,UAAU,SAAS,KAAKE,EAAC,GAAE,QAAQ,IAAEF,GAAE,QAAQ,YAAW,SAAS,KAAGA,GAAE,KAAK,KAAKE,EAAC,GAAEQ,KAAEC,KAAE,SAASX,IAAEE,IAAEE,IAAEH,IAAEO,IAAE;AAAC,aAAQL,KAAE,CAAC,GAAEI,KAAE,GAAEE,KAAEP,GAAE,QAAOK,KAAEE,IAAE,EAAEF;AAAE,SAAGL,IAAE,OAAOK,EAAC,CAAC,IAAEJ,GAAE,KAAK,GAAGH,IAAEE,IAAEE,IAAEH,IAAE,OAAOM,EAAC,GAAE,IAAE,CAAC,IAAEJ,GAAE,KAAK,EAAE;AAAE,WAAOK,GAAE,QAAS,SAASA,IAAE;AAAC,MAAAA,GAAE,MAAM,OAAO,KAAGL,GAAE,KAAK,GAAGH,IAAEE,IAAEE,IAAEH,IAAEO,IAAE,IAAE,CAAC;AAAA,IAAE,CAAE,GAAEL;AAAA,EAAC,EAAEH,IAAEE,IAAEE,IAAEG,IAAEJ,EAAC,IAAEA,GAAE,IAAK,SAASF,IAAE;AAAC,WAAO,GAAGD,IAAEE,IAAEE,IAAEG,IAAEN,IAAEU,EAAC;AAAA,EAAC,CAAE,GAAEX,GAAE,KAAK,IAAI,GAAE,SAASA,IAAEE,IAAEE,IAAE;AAAC,QAAIH,KAAE;AAAE,QAAGD,GAAE,OAAQ,SAASA,IAAEE,IAAE;AAAC,aAAOD,MAAIC,GAAE,QAAQ,IAAI,KAAG,KAAGD,MAAID,KAAEE,GAAE,QAAQ,mBAAkB,EAAE,EAAE,SAAO;AAAA,IAAC,GAAG,CAAC,IAAE;AAAG,aAAOE,GAAE,CAAC,KAAG,OAAKF,KAAE,KAAGA,KAAE,SAAO,MAAIF,GAAE,KAAK,OAAO,IAAE,MAAII,GAAE,CAAC;AAAE,WAAOA,GAAE,CAAC,IAAEF,KAAE,MAAIF,GAAE,KAAK,IAAI,IAAE,MAAII,GAAE,CAAC;AAAA,EAAC,EAAEM,IAAEJ,IAAEM,EAAC,KAAGA,GAAE,CAAC,IAAEN,KAAEM,GAAE,CAAC;AAAC;AAA12D;AAA22D,SAAS,GAAGZ,IAAE;AAAC,SAAO,MAAI,MAAM,UAAU,SAAS,KAAKA,EAAC,IAAE;AAAG;AAArD;AAAsD,SAAS,GAAGA,IAAEE,IAAEE,IAAEH,IAAEO,IAAEL,IAAE;AAAC,MAAII,IAAEE,IAAEC;AAAE,OAAIA,KAAE,OAAO,yBAAyBR,IAAEM,EAAC,KAAG,EAAC,OAAMN,GAAEM,EAAC,EAAC,GAAG,MAAIC,KAAEC,GAAE,MAAIV,GAAE,QAAQ,mBAAkB,SAAS,IAAEA,GAAE,QAAQ,YAAW,SAAS,IAAEU,GAAE,QAAMD,KAAET,GAAE,QAAQ,YAAW,SAAS,IAAG,GAAGC,IAAEO,EAAC,MAAID,KAAE,MAAIC,KAAE,MAAKC,OAAIT,GAAE,KAAK,QAAQU,GAAE,KAAK,IAAE,KAAGD,KAAE,GAAGL,EAAC,IAAE,GAAGJ,IAAEU,GAAE,OAAM,IAAI,IAAE,GAAGV,IAAEU,GAAE,OAAMN,KAAE,CAAC,GAAG,QAAQ,IAAI,IAAE,OAAKK,KAAEN,KAAEM,GAAE,MAAM,IAAI,EAAE,IAAK,SAAST,IAAE;AAAC,WAAO,OAAKA;AAAA,EAAC,CAAE,EAAE,KAAK,IAAI,EAAE,OAAO,CAAC,IAAE,OAAKS,GAAE,MAAM,IAAI,EAAE,IAAK,SAAST,IAAE;AAAC,WAAO,QAAMA;AAAA,EAAC,CAAE,EAAE,KAAK,IAAI,KAAGS,KAAET,GAAE,QAAQ,cAAa,SAAS,IAAG,GAAGO,EAAC,GAAE;AAAC,QAAGJ,MAAGK,GAAE,MAAM,OAAO;AAAE,aAAOC;AAAE,KAACF,KAAE,KAAK,UAAU,KAAGC,EAAC,GAAG,MAAM,8BAA8B,KAAGD,KAAEA,GAAE,OAAO,GAAEA,GAAE,SAAO,CAAC,GAAEA,KAAEP,GAAE,QAAQO,IAAE,MAAM,MAAIA,KAAEA,GAAE,QAAQ,MAAK,KAAK,EAAE,QAAQ,QAAO,GAAG,EAAE,QAAQ,YAAW,GAAG,GAAEA,KAAEP,GAAE,QAAQO,IAAE,QAAQ;AAAA,EAAG;AAAC,SAAOA,KAAE,OAAKE;AAAC;AAA9vB;AAA+vB,SAAS,GAAGT,IAAE;AAAC,SAAO,MAAM,QAAQA,EAAC;AAAC;AAA7B;AAA8B,SAAS,GAAGA,IAAE;AAAC,SAAO,aAAW,OAAOA;AAAC;AAAhC;AAAiC,SAAS,GAAGA,IAAE;AAAC,SAAO,SAAOA;AAAC;AAArB;AAAsB,SAAS,GAAGA,IAAE;AAAC,SAAO,YAAU,OAAOA;AAAC;AAA/B;AAAgC,SAAS,GAAGA,IAAE;AAAC,SAAO,YAAU,OAAOA;AAAC;AAA/B;AAAgC,SAAS,GAAGA,IAAE;AAAC,SAAO,WAASA;AAAC;AAAvB;AAAwB,SAAS,GAAGA,IAAE;AAAC,SAAO,GAAGA,EAAC,KAAG,sBAAoB,GAAGA,EAAC;AAAC;AAA7C;AAA8C,SAAS,GAAGA,IAAE;AAAC,SAAO,YAAU,OAAOA,MAAG,SAAOA;AAAC;AAAzC;AAA0C,SAAS,GAAGA,IAAE;AAAC,SAAO,GAAGA,EAAC,KAAG,oBAAkB,GAAGA,EAAC;AAAC;AAA3C;AAA4C,SAAS,GAAGA,IAAE;AAAC,SAAO,GAAGA,EAAC,MAAI,qBAAmB,GAAGA,EAAC,KAAGA,cAAa;AAAM;AAAlE;AAAmE,SAAS,GAAGA,IAAE;AAAC,SAAO,cAAY,OAAOA;AAAC;AAAjC;AAAkC,SAAS,GAAGA,IAAE;AAAC,SAAO,OAAO,UAAU,SAAS,KAAKA,EAAC;AAAC;AAA9C;AAA+C,SAAS,GAAGA,IAAE;AAAC,SAAOA,KAAE,KAAG,MAAIA,GAAE,SAAS,EAAE,IAAEA,GAAE,SAAS,EAAE;AAAC;AAAnD;AAAoD,EAAE,WAAS,SAASA,IAAE;AAAC,MAAGA,KAAEA,GAAE,YAAY,GAAE,CAAC,GAAGA,EAAC;AAAE,QAAG,GAAG,KAAKA,EAAC,GAAE;AAAC,UAAIE,KAAE,EAAE;AAAI,SAAGF,EAAC,IAAE,WAAU;AAAC,YAAII,KAAE,EAAE,OAAO,MAAM,GAAE,SAAS;AAAE,gBAAQ,MAAM,aAAYJ,IAAEE,IAAEE,EAAC;AAAA,MAAE;AAAA,IAAE;AAAM,SAAGJ,EAAC,IAAE,WAAU;AAAA,MAAC;AAAE,SAAO,GAAGA,EAAC;AAAC,GAAE,EAAE,UAAQ,IAAG,GAAG,SAAO,EAAC,MAAK,CAAC,GAAE,EAAE,GAAE,QAAO,CAAC,GAAE,EAAE,GAAE,WAAU,CAAC,GAAE,EAAE,GAAE,SAAQ,CAAC,GAAE,EAAE,GAAE,OAAM,CAAC,IAAG,EAAE,GAAE,MAAK,CAAC,IAAG,EAAE,GAAE,OAAM,CAAC,IAAG,EAAE,GAAE,MAAK,CAAC,IAAG,EAAE,GAAE,MAAK,CAAC,IAAG,EAAE,GAAE,OAAM,CAAC,IAAG,EAAE,GAAE,SAAQ,CAAC,IAAG,EAAE,GAAE,KAAI,CAAC,IAAG,EAAE,GAAE,QAAO,CAAC,IAAG,EAAE,EAAC,GAAE,GAAG,SAAO,EAAC,SAAQ,QAAO,QAAO,UAAS,SAAQ,UAAS,WAAU,QAAO,MAAK,QAAO,QAAO,SAAQ,MAAK,WAAU,QAAO,MAAK,GAAE,EAAE,QAAM,KAAI,EAAE,UAAQ,IAAG,EAAE,YAAU,IAAG,EAAE,SAAO,IAAG,EAAE,oBAAkB,SAASA,IAAE;AAAC,SAAO,QAAMA;AAAC,GAAE,EAAE,WAAS,IAAG,EAAE,WAAS,IAAG,EAAE,WAAS,SAASA,IAAE;AAAC,SAAO,YAAU,OAAOA;AAAC,GAAE,EAAE,cAAY,IAAG,EAAE,WAAS,IAAG,EAAE,MAAM,WAAS,IAAG,EAAE,WAAS,IAAG,EAAE,SAAO,IAAG,EAAE,MAAM,SAAO,IAAG,EAAE,UAAQ,IAAG,EAAE,MAAM,gBAAc,IAAG,EAAE,aAAW,IAAG,EAAE,cAAY,SAASA,IAAE;AAAC,SAAO,SAAOA,MAAG,aAAW,OAAOA,MAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,MAAG,YAAU,OAAOA,MAAG,WAASA;AAAC,GAAE,EAAE,WAAS;AAAI,IAAI,KAAG,CAAC,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,OAAM,KAAK;AAAE,SAAS,KAAI;AAAC,MAAIA,KAAE,oBAAI,QAAKE,KAAE,CAAC,GAAGF,GAAE,SAAS,CAAC,GAAE,GAAGA,GAAE,WAAW,CAAC,GAAE,GAAGA,GAAE,WAAW,CAAC,CAAC,EAAE,KAAK,GAAG;AAAE,SAAO,CAACA,GAAE,QAAQ,GAAE,GAAGA,GAAE,SAAS,CAAC,GAAEE,EAAC,EAAE,KAAK,GAAG;AAAC;AAA1I;AAA2I,SAAS,GAAGF,IAAEE,IAAE;AAAC,SAAO,OAAO,UAAU,eAAe,KAAKF,IAAEE,EAAC;AAAC;AAAxD;AAAyD,EAAE,MAAI,WAAU;AAAC,UAAQ,IAAI,WAAU,GAAG,GAAE,EAAE,OAAO,MAAM,GAAE,SAAS,CAAC;AAAE,GAAE,EAAE,WAAS,KAAI,EAAE,UAAQ,SAASF,IAAEE,IAAE;AAAC,MAAG,CAACA,MAAG,CAAC,GAAGA,EAAC;AAAE,WAAOF;AAAE,WAAQI,KAAE,OAAO,KAAKF,EAAC,GAAED,KAAEG,GAAE,QAAOH;AAAK,IAAAD,GAAEI,GAAEH,EAAC,CAAC,IAAEC,GAAEE,GAAEH,EAAC,CAAC;AAAE,SAAOD;AAAC;AAAE,IAAI,KAAG,eAAa,OAAO,SAAO,OAAO,uBAAuB,IAAE;AAAO,SAAS,GAAGA,IAAEE,IAAE;AAAC,MAAG,CAACF,IAAE;AAAC,QAAII,KAAE,IAAI,MAAM,yCAAyC;AAAE,IAAAA,GAAE,SAAOJ,IAAEA,KAAEI;AAAA,EAAE;AAAC,SAAOF,GAAEF,EAAC;AAAC;AAArG;AAAsG,EAAE,YAAU,SAASA,IAAE;AAAC,MAAG,cAAY,OAAOA;AAAE,UAAM,IAAI,UAAU,kDAAkD;AAAE,MAAG,MAAIA,GAAE,EAAE,GAAE;AAAC,QAAIE;AAAE,QAAG,cAAY,QAAOA,KAAEF,GAAE,EAAE;AAAG,YAAM,IAAI,UAAU,+DAA+D;AAAE,WAAO,OAAO,eAAeE,IAAE,IAAG,EAAC,OAAMA,IAAE,YAAW,OAAG,UAAS,OAAG,cAAa,KAAE,CAAC,GAAEA;AAAA,EAAC;AAAC,WAASA,KAAG;AAAC,aAAQA,IAAEE,IAAEH,KAAE,IAAI,QAAS,SAASD,IAAEC,IAAE;AAAC,MAAAC,KAAEF,IAAEI,KAAEH;AAAA,IAAE,CAAE,GAAEO,KAAE,CAAC,GAAEL,KAAE,GAAEA,KAAE,UAAU,QAAOA;AAAI,MAAAK,GAAE,KAAK,UAAUL,EAAC,CAAC;AAAE,IAAAK,GAAE,KAAM,SAASR,IAAEC,IAAE;AAAC,MAAAD,KAAEI,GAAEJ,EAAC,IAAEE,GAAED,EAAC;AAAA,IAAE,CAAE;AAAE,QAAG;AAAC,MAAAD,GAAE,MAAM,QAAM,GAAEQ,EAAC;AAAA,IAAE,SAAOR,IAAE;AAAC,MAAAI,GAAEJ,EAAC;AAAA,IAAE;AAAC,WAAOC;AAAA,EAAC;AAAnM,SAAAC,IAAA;AAAoM,SAAO,OAAO,eAAeA,IAAE,OAAO,eAAeF,EAAC,CAAC,GAAE,MAAI,OAAO,eAAeE,IAAE,IAAG,EAAC,OAAMA,IAAE,YAAW,OAAG,UAAS,OAAG,cAAa,KAAE,CAAC,GAAE,OAAO,iBAAiBA,IAAE,GAAGF,EAAC,CAAC;AAAC,GAAE,EAAE,UAAU,SAAO,IAAG,EAAE,cAAY,SAASA,IAAE;AAAC,MAAG,cAAY,OAAOA;AAAE,UAAM,IAAI,UAAU,kDAAkD;AAAE,WAASE,KAAG;AAAC,aAAQA,KAAE,CAAC,GAAEE,KAAE,GAAEA,KAAE,UAAU,QAAOA;AAAI,MAAAF,GAAE,KAAK,UAAUE,EAAC,CAAC;AAAE,QAAIH,KAAEC,GAAE,IAAI;AAAE,QAAG,cAAY,OAAOD;AAAE,YAAM,IAAI,UAAU,4CAA4C;AAAE,QAAIO,KAAE,QAAM,GAAEL,KAAE,kCAAU;AAAC,aAAOF,GAAE,MAAMO,IAAE,SAAS;AAAA,IAAC,GAAtC;AAAwC,IAAAR,GAAE,MAAM,QAAM,GAAEE,EAAC,EAAE,KAAM,SAASF,IAAE;AAAC,QAAE,SAASG,GAAE,KAAK,MAAK,MAAKH,EAAC,CAAC;AAAA,IAAE,GAAI,SAASA,IAAE;AAAC,QAAE,SAAS,GAAG,KAAK,MAAKA,IAAEG,EAAC,CAAC;AAAA,IAAE,CAAE;AAAA,EAAE;AAAxV,SAAAD,IAAA;AAAyV,SAAO,OAAO,eAAeA,IAAE,OAAO,eAAeF,EAAC,CAAC,GAAE,OAAO,iBAAiBE,IAAE,GAAGF,EAAC,CAAC,GAAEE;AAAC;;;ACCrtb,EAAE;AAAQ,EAAE;AAAY,EAAE;AAAS,EAAE;AAAU,EAAE;AAAO,EAAE;AAAS,EAAE;AAAQ,EAAE;AAAQ,EAAE;AAAU,EAAE;AAAS,EAAE;AAAO,EAAE;AAAQ,EAAE;AAAW,EAAE;AAAO,EAAE;AAAkB,EAAE;AAAS,EAAE;AAAS,EAAE;AAAY,EAAE;AAAS,EAAE;AAAS,EAAE;AAAS,EAAE;AAAY,EAAE;AAAI,EAAE;AAEtQ,IAAI,UAAU,EAAE;AAChB,IAAI,cAAc,EAAE;AACpB,IAAI,WAAW,EAAE;AACjB,IAAI,YAAY,EAAE;AAClB,IAAI,SAAS,EAAE;AACf,IAAI,WAAW,EAAE;AACjB,IAAI,UAAU,EAAE;AAChB,IAAI,UAAU,EAAE;AAChB,IAAI,YAAY,EAAE;AAClB,IAAI,WAAW,EAAE;AACjB,IAAI,SAAS,EAAE;AACf,IAAI,UAAU,EAAE;AAChB,IAAI,aAAa,EAAE;AACnB,IAAI,SAAS,EAAE;AACf,IAAI,oBAAoB,EAAE;AAC1B,IAAI,WAAW,EAAE;AACjB,IAAI,WAAW,EAAE;AACjB,IAAI,cAAc,EAAE;AACpB,IAAI,WAAW,EAAE;AACjB,IAAI,WAAW,EAAE;AACjB,IAAI,WAAW,EAAE;AACjB,IAAI,cAAc,EAAE;AACpB,IAAI,MAAM,EAAE;AACZ,IAAI,YAAY,EAAE;AAClB,IAAI,QAAQ,EAAE;AAEd,IAAM,cAAc,KAAK;AACzB,IAAM,cAAc,KAAK;;;AC3BzB,IAAIe,WAAU,EAAE;AAChB,IAAIC,eAAc,EAAE;AACpB,IAAIC,YAAW,EAAE;AACjB,IAAIC,aAAY,EAAE;AAClB,IAAIC,UAAS,EAAE;AACf,IAAIC,YAAW,EAAE;AACjB,IAAIC,WAAU,EAAE;AAChB,IAAIC,WAAU,EAAE;AAChB,IAAIC,aAAY,EAAE;AAClB,IAAIC,YAAW,EAAE;AACjB,IAAIC,UAAS,EAAE;AACf,IAAIC,WAAU,EAAE;AAChB,IAAIC,cAAa,EAAE;AACnB,IAAIC,UAAS,EAAE;AACf,IAAIC,qBAAoB,EAAE;AAC1B,IAAIC,YAAW,EAAE;AACjB,IAAIC,YAAW,EAAE;AACjB,IAAIC,eAAc,EAAE;AACpB,IAAIC,YAAW,EAAE;AACjB,IAAIC,YAAW,EAAE;AACjB,IAAIC,YAAW,EAAE;AACjB,IAAIC,eAAc,EAAE;AACpB,IAAIC,OAAM,EAAE;AACZ,IAAIC,aAAY,EAAE;AAClB,IAAIC,SAAQ,EAAE;AAEd,IAAMC,eAAc,EAAE,cAAc,WAAW;AAC/C,IAAMC,eAAc,EAAE,cAAc,WAAW;;;AC7BxC,IAAM,sBAAsB,OAAO,IAAI,4BAA4B;AACnE,IAAM,+BAA+B,OAAO,IAAI,uCAAuC;AAEvF,IAAe,aAAf,MAAe,mBAAkB,MAAM;AAAA,EACtC,SAA6B;AACnC,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,IACf;AAAA,EACD;AAAA,EAEA,CAAW,mBAAmB,EAAE,OAAe,SAAiC;AAC/E,WAAO,GAAG,KAAK,4BAA4B,EAAE,OAAO,OAAO,CAAC;AAAA,EAAK,KAAK,MAAO,MAAM,KAAK,MAAO,QAAQ,IAAI,CAAC,CAAC;AAAA,EAC9G;AAGD;AAb8C;AAAvC,IAAe,YAAf;;;ACiBA,IAAe,uBAAf,MAAe,6BAAyC,UAAU;AAAA,EAIjE,YAAY,YAAkC,SAAiB,OAAU;AAC/E,UAAM,OAAO;AACb,SAAK,aAAa;AAClB,SAAK,QAAQ;AAAA,EACd;AAAA,EAEgB,SAA0C;AACzD,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,YAAY,KAAK;AAAA,MACjB,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK;AAAA,IACf;AAAA,EACD;AACD;AAlByE;AAAlE,IAAe,sBAAf;;;AClBA,IAAM,2BAAN,MAAM,iCAA6C,oBAAuB;AAAA,EAGzE,YAAY,YAAkC,SAAiB,OAAU,UAAkB;AACjG,UAAM,YAAY,SAAS,KAAK;AAChC,SAAK,WAAW;AAAA,EACjB;AAAA,EAEgB,SAA8C;AAC7D,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,YAAY,KAAK;AAAA,MACjB,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,SAAS,KAAK;AAAA,IACf;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,aAAa,QAAQ,QAAQ,KAAK,YAAY,QAAQ;AAC5D,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,6BAA6B,UAAU,KAAK,SAAS;AAAA,IAC7E;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,EAAE;AAE3F,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AACxD,UAAM,QAAQpB,SAAQ,KAAK,OAAO,UAAU,EAAE,QAAQ,OAAO,OAAO;AAEpE,UAAM,SAAS,GAAG,QAAQ,QAAQ,2BAA2B,SAAS,CAAC,MAAM,UAAU;AACvF,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,gBAAgB;AAAA,IAAO,QAAQ,QAAQ,cAAc,QAAQ,CAAC,GAAG,QAAQ,QAAQ,KAAK,UAAU,SAAS,CAAC;AAChH,UAAM,aAAa;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClF,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,aAAa;AAAA,EAAK,UAAU;AAAA,EAChE;AACD;AAnCiF;AAA1E,IAAM,0BAAN;;;ACLA,IAAM,UAAN,MAAM,QAAmC;AAAA,EAKvC,YAAY,SAAkB,OAAW,OAAW;AAC3D,SAAK,UAAU;AACf,QAAI,SAAS;AACZ,WAAK,QAAQ;AAAA,IACd,OAAO;AACN,WAAK,QAAQ;AAAA,IACd;AAAA,EACD;AAAA,EAEO,OAA4C;AAClD,WAAO,KAAK;AAAA,EACb;AAAA,EAEO,QAA8C;AACpD,WAAO,CAAC,KAAK;AAAA,EACd;AAAA,EAEO,SAAY;AAClB,QAAI,KAAK,KAAK;AAAG,aAAO,KAAK;AAC7B,UAAM,KAAK;AAAA,EACZ;AAAA,EAEA,OAAc,GAA+B,OAAwB;AACpE,WAAO,IAAI,QAAa,MAAM,KAAK;AAAA,EACpC;AAAA,EAEA,OAAc,IAAgC,OAAwB;AACrE,WAAO,IAAI,QAAa,OAAO,QAAW,KAAK;AAAA,EAChD;AACD;AAlCgD;AAAzC,IAAM,SAAN;;;ARiBA,SAAS,eACf,KACA,SACA,WACA,kBACiB;AACjB,SAAO;AAAA,IACN,IAAI,OAAU,QAAc;AAC3B,UAAI,CAAC,QAAQ;AACZ,eAAO,OAAO;AAAA,UACb,IAAI;AAAA,YACH;AAAA,YACA,kBAAkB,WAAW;AAAA,YAC7B;AAAA,YACA;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,YAAM,aAAa,MAAM,QAAQ,GAAG;AAEpC,YAAM,QAAQ,aAAa,IAAI,IAAI,CAACqB,OAAM,IAAI,QAAQA,EAAC,CAAC,IAAI,IAAI,QAAQ,GAAG;AAE3E,YAAM,YAAY,iBAAyB,SAAS,OAAO,UAAU,IAAI,QAAQ,OAAO,QAAQ;AAEhG,UAAI,WAAW;AACd,eAAO,UAAU,SAAS,EAAE,IAAI,KAAK;AAAA,MACtC;AAEA,aAAO,OAAO,GAAG,KAAK;AAAA,IACvB;AAAA,EACD;AACD;AAhCgB;AAkChB,SAAS,iBAAoE,SAA8B,OAAY,YAAqB;AAC3I,MAAI,QAAQ,OAAO,QAAW;AAC7B,WAAO,aAAa,CAAC,MAAM,KAAK,CAAC,QAAa,CAAC,GAAG,IAAI,QAAQ,KAAK;AAAA,EACpE;AAEA,MAAI,OAAO,QAAQ,OAAO,YAAY;AACrC,WAAO,QAAQ,GAAG,KAAK;AAAA,EACxB;AAEA,SAAO,UAAU,QAAQ;AAC1B;AAVS;;;ASnDT,IAAI,oBAAoB;AAMjB,SAAS,2BAA2B,SAAkB;AAC5D,sBAAoB;AACrB;AAFgB;AAOT,SAAS,6BAA6B;AAC5C,SAAO;AACR;AAFgB;;;ACVT,SAAS,SAAkD,WAAiB;AAClF,SAAO,OAAO,cAAc,aAAa,UAAU,IAAI;AACxD;AAFgB;;;ACWT,IAAe,iBAAf,MAAe,eAAiB;AAAA,EAO/B,YAAY,mBAAqC,CAAC,GAAG,cAAyC,CAAC,GAAG;AAHzG,SAAU,cAAyC,CAAC;AACpD,SAAU,sBAAwD;AAGjE,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAAA,EACzB;AAAA,EAEO,UAAU,QAAsB;AACtC,SAAK,SAAS;AACd,WAAO;AAAA,EACR;AAAA,EAEO,SAAS,UAA4B,KAAK,kBAAiD;AACjG,WAAO,IAAI,eAAe,CAAC,IAAI,iBAAiB,QAAW,OAAO,GAAG,KAAK,MAAM,CAAC,GAAG,OAAO;AAAA,EAC5F;AAAA,EAEO,SAAS,UAA4B,KAAK,kBAA4C;AAC5F,WAAO,IAAI,eAAe,CAAC,IAAI,iBAAiB,MAAM,OAAO,GAAG,KAAK,MAAM,CAAC,GAAG,OAAO;AAAA,EACvF;AAAA,EAEO,QAAQ,UAA4B,KAAK,kBAAwD;AACvG,WAAO,IAAI,eAAe,CAAC,IAAI,iBAAiB,OAAO,GAAG,KAAK,MAAM,CAAC,GAAG,OAAO;AAAA,EACjF;AAAA,EAEO,MAAM,UAA4B,KAAK,kBAAuC;AACpF,WAAO,IAAI,eAAoB,KAAK,MAAM,GAAG,OAAO;AAAA,EACrD;AAAA,EAEO,IAAI,UAA4B,KAAK,kBAAmC;AAC9E,WAAO,IAAI,aAAgB,KAAK,MAAM,GAAG,OAAO;AAAA,EACjD;AAAA,EAEO,MAAS,YAAgE;AAC/E,WAAO,IAAI,eAAsB,CAAC,KAAK,MAAM,GAAG,GAAG,UAAU,GAAG,KAAK,gBAAgB;AAAA,EACtF;AAAA,EAIO,UAAa,IAAqB,UAA4B,KAAK,kBAAoC;AAC7G,WAAO,KAAK;AAAA,MACX;AAAA,QACC,KAAK,CAAC,UAAU,OAAO,GAAG,GAAG,KAAK,CAAiB;AAAA,MACpD;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAIO,QACN,IACA,UAA4B,KAAK,kBACd;AACnB,WAAO,KAAK;AAAA,MACX;AAAA,QACC,KAAK;AAAA,MACN;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEO,QACN,OACA,UAA4B,KAAK,kBACS;AAC1C,WAAO,IAAI,iBAAiB,KAAK,MAAM,GAAsD,OAAO,OAAO;AAAA,EAC5G;AAAA,EAEO,KACN,KACA,SACA,kBACO;AACP,WAAO,KAAK,cAAc,eAA6B,KAAK,SAAS,MAAyB,gBAAgB,CAAC;AAAA,EAChH;AAAA,EAEO,SAAS,aAA2B;AAC1C,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,cAAc;AACpB,WAAO;AAAA,EACR;AAAA,EAEO,IAAI,OAAsC;AAChD,QAAI,SAAS,KAAK,OAAO,KAAK;AAC9B,QAAI,OAAO,MAAM;AAAG,aAAO;AAE3B,eAAW,cAAc,KAAK,aAAa;AAC1C,eAAS,WAAW,IAAI,OAAO,OAAY,KAAK,MAAM;AACtD,UAAI,OAAO,MAAM;AAAG;AAAA,IACrB;AAEA,WAAO;AAAA,EACR;AAAA,EAEO,MAAuB,OAAmB;AAGhD,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,KAAK,OAAO,KAAK,EAAE,OAAO;AAAA,IAClC;AAEA,WAAO,KAAK,YAAY,OAAO,CAACC,IAAG,eAAe,WAAW,IAAIA,EAAC,EAAE,OAAO,GAAG,KAAK,OAAO,KAAK,EAAE,OAAO,CAAC;AAAA,EAC1G;AAAA,EAEO,GAAoB,OAA4B;AACtD,WAAO,KAAK,IAAI,KAAK,EAAE,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,qBAAqB,qBAA6D;AACxF,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,sBAAsB;AAC5B,WAAO;AAAA,EACR;AAAA,EAEO,uBAAuB;AAC7B,WAAO,SAAS,KAAK,mBAAmB;AAAA,EACzC;AAAA,EAEA,IAAc,uBAAgC;AAC7C,WAAO,SAAS,KAAK,mBAAmB,KAAK,2BAA2B;AAAA,EACzE;AAAA,EAEU,QAAc;AACvB,UAAM,QAAc,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,kBAAkB,KAAK,WAAW,CAAC;AACjG,UAAM,sBAAsB,KAAK;AACjC,WAAO;AAAA,EACR;AAAA,EAIU,cAAc,YAA4B,mBAAqC,KAAK,kBAAwB;AACrH,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,mBAAmB;AACzB,UAAM,cAAc,MAAM,YAAY,OAAO,UAAU;AACvD,WAAO;AAAA,EACR;AACD;AAlJuC;AAAhC,IAAe,gBAAf;;;ACdP,OAAO,mBAAmB;AAC1B,OAAO,cAAc;AAEd,SAAS,SAAS,OAAkB;AAC1C,MAAI,MAAM,SAAS;AAAG,WAAO;AAC7B,QAAMC,eAAc,SAAS,OAAO,aAAa;AACjD,SAAOA,aAAY,WAAW,MAAM;AACrC;AAJgB;;;ACDT,SAAS,SAASpC,IAAoBqC,IAA6B;AACzE,SAAOrC,KAAIqC;AACZ;AAFgB;AAMT,SAAS,gBAAgBrC,IAAoBqC,IAA6B;AAChF,SAAOrC,MAAKqC;AACb;AAFgB;AAMT,SAAS,YAAYrC,IAAoBqC,IAA6B;AAC5E,SAAOrC,KAAIqC;AACZ;AAFgB;AAMT,SAAS,mBAAmBrC,IAAoBqC,IAA6B;AACnF,SAAOrC,MAAKqC;AACb;AAFgB;AAMT,SAAS,MAAMrC,IAAoBqC,IAA6B;AACtE,SAAOrC,OAAMqC;AACd;AAFgB;AAMT,SAAS,SAASrC,IAAoBqC,IAA6B;AACzE,SAAOrC,OAAMqC;AACd;AAFgB;;;ACZhB,SAAS,sBACR,YACA,MACA,UACA,QACA,SACmB;AACnB,SAAO;AAAA,IACN,IAAI,OAAY;AACf,aAAO,WAAW,MAAM,QAAQ,MAAM,IACnC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,SAAS,WAAW,wBAAwB,OAAO,QAAQ,CAAC;AAAA,IAC7G;AAAA,EACD;AACD;AAdS;AAgBF,SAAS,oBAAuB,OAAe,SAA8C;AACnG,QAAM,WAAW,qBAAqB,KAAK;AAC3C,SAAO,sBAAsB,UAAU,+BAA+B,UAAU,OAAO,OAAO;AAC/F;AAHgB;AAKT,SAAS,2BAA8B,OAAe,SAA8C;AAC1G,QAAM,WAAW,sBAAsB,KAAK;AAC5C,SAAO,sBAAsB,iBAAiB,sCAAsC,UAAU,OAAO,OAAO;AAC7G;AAHgB;AAKT,SAAS,uBAA0B,OAAe,SAA8C;AACtG,QAAM,WAAW,qBAAqB,KAAK;AAC3C,SAAO,sBAAsB,aAAa,kCAAkC,UAAU,OAAO,OAAO;AACrG;AAHgB;AAKT,SAAS,8BAAiC,OAAe,SAA8C;AAC7G,QAAM,WAAW,sBAAsB,KAAK;AAC5C,SAAO,sBAAsB,oBAAoB,yCAAyC,UAAU,OAAO,OAAO;AACnH;AAHgB;AAKT,SAAS,iBAAoB,OAAe,SAA8C;AAChG,QAAM,WAAW,uBAAuB,KAAK;AAC7C,SAAO,sBAAsB,OAAO,4BAA4B,UAAU,OAAO,OAAO;AACzF;AAHgB;AAKT,SAAS,oBAAuB,OAAe,SAA8C;AACnG,QAAM,WAAW,uBAAuB,KAAK;AAC7C,SAAO,sBAAsB,UAAU,+BAA+B,UAAU,OAAO,OAAO;AAC/F;AAHgB;AAKT,SAAS,iBAAoB,OAAe,WAAmB,SAA8C;AACnH,QAAM,WAAW,sBAAsB,KAAK,yBAAyB,SAAS;AAC9E,SAAO;AAAA,IACN,IAAI,OAAY;AACf,aAAO,MAAM,UAAU,SAAS,MAAM,SAAS,YAC5C,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,4BAA4B,SAAS,WAAW,wBAAwB,OAAO,QAAQ,CAAC;AAAA,IACnI;AAAA,EACD;AACD;AATgB;AAWT,SAAS,0BAA6B,OAAe,KAAa,SAA8C;AACtH,QAAM,WAAW,sBAAsB,KAAK,0BAA0B,GAAG;AACzE,SAAO;AAAA,IACN,IAAI,OAAY;AACf,aAAO,MAAM,UAAU,SAAS,MAAM,UAAU,MAC7C,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI,wBAAwB,qCAAqC,SAAS,WAAW,wBAAwB,OAAO,QAAQ;AAAA,MAC7H;AAAA,IACH;AAAA,EACD;AACD;AAXgB;AAaT,SAAS,0BAA6B,YAAoB,WAAmB,SAA8C;AACjI,QAAM,WAAW,qBAAqB,UAAU,yBAAyB,SAAS;AAClF,SAAO;AAAA,IACN,IAAI,OAAY;AACf,aAAO,MAAM,SAAS,cAAc,MAAM,SAAS,YAChD,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI,wBAAwB,qCAAqC,SAAS,WAAW,wBAAwB,OAAO,QAAQ;AAAA,MAC7H;AAAA,IACH;AAAA,EACD;AACD;AAXgB;AAaT,SAAS,YAAY,SAAoD;AAC/E,SAAO;AAAA,IACN,IAAI,OAAkB;AACrB,aAAO,SAAS,KAAK,IAClB,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AAfgB;;;ACnGT,IAAM,yBAAN,MAAM,+BAA8B,UAAU;AAAA,EAG7C,YAAY,QAAoC,kBAAqC;AAC3F,UAAM,kBAAkB,WAAW,6BAA6B;AAEhE,SAAK,SAAS;AAAA,EACf;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,2BAA2B,SAAS;AAAA,IAC5D;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,GAAG,SAAS,KAAK;AAE1G,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AAExD,UAAM,SAAS,GAAG,QAAQ,QAAQ,yBAAyB,SAAS,CAAC,KAAK,QAAQ,QAAQ,KAAK,OAAO,OAAO,SAAS,GAAG,QAAQ,CAAC;AAClI,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,SAAS,KAAK,OAClB,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACtB,YAAM,WAAW,uBAAsB,eAAe,KAAK,OAAO;AAClE,YAAM,OAAO,MAAM,4BAA4B,EAAE,QAAQ,GAAG,UAAU,EAAE,QAAQ,OAAO,OAAO;AAE9F,aAAO,UAAU,QAAQ,GAAG,OAAO,GAAG,IAAI;AAAA,IAC3C,CAAC,EACA,KAAK,MAAM;AACb,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA;AAAA,EAAO,MAAM;AAAA,EAC5C;AAAA,EAEA,OAAe,eAAe,KAAkB,SAAyC;AACxF,QAAI,OAAO,QAAQ;AAAU,aAAO,QAAQ,QAAQ,IAAI,GAAG,IAAI,QAAQ;AACvE,QAAI,OAAO,QAAQ;AAAU,aAAO,IAAI,QAAQ,QAAQ,IAAI,SAAS,GAAG,QAAQ,CAAC;AACjF,WAAO,IAAI,QAAQ,QAAQ,UAAU,QAAQ,CAAC,IAAI,IAAI,WAAW;AAAA,EAClE;AACD;AApCqD;AAA9C,IAAM,wBAAN;;;ACAA,IAAM,mBAAN,MAAM,yBAAwB,UAAU;AAAA,EAIvC,YAAY,WAAmB,SAAiB,OAAgB;AACtE,UAAM,OAAO;AAEb,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACd;AAAA,EAEgB,SAAmC;AAClD,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,SAAS;AAAA,MACT,WAAW,KAAK;AAAA,MAChB,OAAO,KAAK;AAAA,IACb;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,YAAY,QAAQ,QAAQ,KAAK,WAAW,QAAQ;AAC1D,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,qBAAqB,SAAS,KAAK,SAAS;AAAA,IACpE;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,GAAG,SAAS,KAAK;AAE1G,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AACxD,UAAM,QAAQxB,SAAQ,KAAK,OAAO,UAAU,EAAE,QAAQ,OAAO,OAAO;AAEpE,UAAM,SAAS,GAAG,QAAQ,QAAQ,mBAAmB,SAAS,CAAC,MAAM,SAAS;AAC9E,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,aAAa;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClF,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,UAAU;AAAA,EAC9C;AACD;AApC+C;AAAxC,IAAM,kBAAN;;;ACgBA,IAAM,kBAAN,MAAM,wBAA2D,cAAiB;AAAA,EAGjF,YAAY,WAA6B,mBAAqC,CAAC,GAAG,cAAyC,CAAC,GAAG;AACrI,UAAM,kBAAkB,WAAW;AACnC,SAAK,YAAY;AAAA,EAClB;AAAA,EAEO,eACN,QACA,UAA4B,KAAK,kBACqC;AACtE,WAAO,KAAK,cAAc,oBAAoB,QAAQ,OAAO,CAAmB;AAAA,EACjF;AAAA,EAEO,sBACN,QACA,UAA4B,KAAK,kBACuB;AACxD,WAAO,KAAK,cAAc,2BAA2B,QAAQ,OAAO,CAAmB;AAAA,EACxF;AAAA,EAEO,kBACN,QACA,UAA4B,KAAK,kBACW;AAC5C,WAAO,KAAK,cAAc,uBAAuB,QAAQ,OAAO,CAAmB;AAAA,EACpF;AAAA,EAEO,yBACN,QACA,UAA4B,KAAK,kBACQ;AACzC,WAAO,KAAK,cAAc,8BAA8B,QAAQ,OAAO,CAAmB;AAAA,EAC3F;AAAA,EAEO,YAA8B,QAAW,UAA4B,KAAK,kBAAoD;AACpI,WAAO,KAAK,cAAc,iBAAiB,QAAQ,OAAO,CAAmB;AAAA,EAC9E;AAAA,EAEO,eAAiC,QAAW,UAA4B,KAAK,kBAAoD;AACvI,WAAO,KAAK,cAAc,oBAAoB,QAAQ,OAAO,CAAmB;AAAA,EACjF;AAAA,EAEO,YACN,OACA,WACA,UAA4B,KAAK,kBACmG;AACpI,WAAO,KAAK,cAAc,iBAAiB,OAAO,WAAW,OAAO,CAAmB;AAAA,EACxF;AAAA,EAEO,qBACN,SACA,OACA,UAA4B,KAAK,kBACqF;AACtH,WAAO,KAAK,cAAc,0BAA0B,SAAS,OAAO,OAAO,CAAmB;AAAA,EAC/F;AAAA,EAEO,qBACN,YACA,WACA,UAA4B,KAAK,kBACqF;AACtH,WAAO,KAAK,cAAc,0BAA0B,YAAY,WAAW,OAAO,CAAmB;AAAA,EACtG;AAAA,EAEO,OAAO,UAA4B,KAAK,kBAAwB;AACtE,WAAO,KAAK,cAAc,YAAY,OAAO,CAAmB;AAAA,EACjE;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EACrG;AAAA,EAEU,OAAO,QAAqE;AACrF,QAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC3B,aAAO,OAAO,IAAI,IAAI,gBAAgB,cAAc,KAAK,iBAAiB,WAAW,qBAAqB,MAAM,CAAC;AAAA,IAClH;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,MAAW;AAAA,IAC7B;AAEA,UAAM,SAAgC,CAAC;AACvC,UAAM,cAAiB,CAAC;AAExB,aAASf,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACvC,YAAM,SAAS,KAAK,UAAU,IAAI,OAAOA,EAAC,CAAC;AAC3C,UAAI,OAAO,KAAK;AAAG,oBAAY,KAAK,OAAO,KAAK;AAAA;AAC3C,eAAO,KAAK,CAACA,IAAG,OAAO,KAAM,CAAC;AAAA,IACpC;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,QAAQ,KAAK,gBAAgB,CAAC;AAAA,EACvE;AACD;AAlGyF;AAAlF,IAAM,iBAAN;;;ACLP,SAAS,iBACR,YACA,MACA,UACA,QACA,SACsB;AACtB,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,WAAW,OAAO,MAAM,IAC5B,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,SAAS,WAAW,wBAAwB,OAAO,QAAQ,CAAC;AAAA,IAC7G;AAAA,EACD;AACD;AAdS;AAgBF,SAAS,eAAe,OAAe,SAAiD;AAC9F,QAAM,WAAW,cAAc,KAAK;AACpC,SAAO,iBAAiB,UAAU,yBAAyB,UAAU,OAAO,OAAO;AACpF;AAHgB;AAKT,SAAS,sBAAsB,OAAe,SAAiD;AACrG,QAAM,WAAW,eAAe,KAAK;AACrC,SAAO,iBAAiB,iBAAiB,gCAAgC,UAAU,OAAO,OAAO;AAClG;AAHgB;AAKT,SAAS,kBAAkB,OAAe,SAAiD;AACjG,QAAM,WAAW,cAAc,KAAK;AACpC,SAAO,iBAAiB,aAAa,4BAA4B,UAAU,OAAO,OAAO;AAC1F;AAHgB;AAKT,SAAS,yBAAyB,OAAe,SAAiD;AACxG,QAAM,WAAW,eAAe,KAAK;AACrC,SAAO,iBAAiB,oBAAoB,mCAAmC,UAAU,OAAO,OAAO;AACxG;AAHgB;AAKT,SAAS,YAAY,OAAe,SAAiD;AAC3F,QAAM,WAAW,gBAAgB,KAAK;AACtC,SAAO,iBAAiB,OAAO,sBAAsB,UAAU,OAAO,OAAO;AAC9E;AAHgB;AAKT,SAAS,eAAe,OAAe,SAAiD;AAC9F,QAAM,WAAW,gBAAgB,KAAK;AACtC,SAAO,iBAAiB,UAAU,yBAAyB,UAAU,OAAO,OAAO;AACpF;AAHgB;AAKT,SAAS,kBAAkB,SAAiB,SAAiD;AACnG,QAAM,WAAW,cAAc,OAAO;AACtC,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,QAAQ,YAAY,KACxB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,4BAA4B,SAAS,WAAW,2BAA2B,OAAO,QAAQ,CAAC;AAAA,IACtI;AAAA,EACD;AACD;AATgB;;;AC9CT,IAAM,mBAAN,MAAM,yBAA0C,cAAiB;AAAA,EAChE,SAAS,QAAgB,UAA4B,KAAK,kBAAwB;AACxF,WAAO,KAAK,cAAc,eAAe,QAAQ,OAAO,CAAmB;AAAA,EAC5E;AAAA,EAEO,gBAAgB,QAAgB,UAA4B,KAAK,kBAAwB;AAC/F,WAAO,KAAK,cAAc,sBAAsB,QAAQ,OAAO,CAAmB;AAAA,EACnF;AAAA,EAEO,YAAY,QAAgB,UAA4B,KAAK,kBAAwB;AAC3F,WAAO,KAAK,cAAc,kBAAkB,QAAQ,OAAO,CAAmB;AAAA,EAC/E;AAAA,EAEO,mBAAmB,QAAgB,UAA4B,KAAK,kBAAwB;AAClG,WAAO,KAAK,cAAc,yBAAyB,QAAQ,OAAO,CAAmB;AAAA,EACtF;AAAA,EAEO,MAAwB,QAAW,UAA4B,KAAK,kBAAsC;AAChH,WAAO,KAAK,cAAc,YAAY,QAAQ,OAAO,CAAmB;AAAA,EACzE;AAAA,EAEO,SAAS,QAAgB,UAA4B,KAAK,kBAAwB;AACxF,WAAO,KAAK,cAAc,eAAe,QAAQ,OAAO,CAAmB;AAAA,EAC5E;AAAA,EAEO,SAAS,UAA4B,KAAK,kBAAwB;AACxE,WAAO,KAAK,mBAAmB,IAAI,OAAO;AAAA,EAC3C;AAAA,EAEO,SAAS,UAA4B,KAAK,kBAAwB;AACxE,WAAO,KAAK,SAAS,IAAI,OAAO;AAAA,EACjC;AAAA,EAEO,YAAY,QAAgB,UAA4B,KAAK,kBAAwB;AAC3F,WAAO,KAAK,cAAc,kBAAkB,QAAQ,OAAO,CAAmB;AAAA,EAC/E;AAAA,EAEO,IAAI,UAA4B,KAAK,kBAAwB;AACnE,WAAO,KAAK,UAAU,CAAC,UAAW,QAAQ,IAAI,CAAC,QAAQ,OAAa,OAAO;AAAA,EAC5E;AAAA,EAEO,KAAK,MAAc,UAA4B,KAAK,kBAAwB;AAClF,WAAO,KAAK,UAAU,CAAC,UAAU,OAAO,OAAO,MAAM,KAAK,GAAQ,OAAO;AAAA,EAC1E;AAAA,EAEO,MAAM,MAAc,UAA4B,KAAK,kBAAwB;AACnF,WAAO,KAAK,UAAU,CAAC,UAAU,OAAO,QAAQ,MAAM,KAAK,GAAQ,OAAO;AAAA,EAC3E;AAAA,EAEU,OAAO,OAA4C;AAC5D,WAAO,OAAO,UAAU,WACrB,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,gBAAgB,cAAc,KAAK,iBAAiB,WAAW,+BAA+B,KAAK,CAAC;AAAA,EACvH;AACD;AAtDwE;AAAjE,IAAM,kBAAN;;;ACRA,SAAS,YAAY,SAAwD;AACnF,SAAO;AAAA,IACN,IAAI,OAAgB;AACnB,aAAO,QACJ,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,sBAAsB,SAAS,WAAW,yBAAyB,OAAO,MAAM,CAAC;AAAA,IAC5H;AAAA,EACD;AACD;AARgB;AAUT,SAAS,aAAa,SAAyD;AACrF,SAAO;AAAA,IACN,IAAI,OAAgB;AACnB,aAAO,QACJ,OAAO,IAAI,IAAI,wBAAwB,uBAAuB,SAAS,WAAW,yBAAyB,OAAO,OAAO,CAAC,IAC1H,OAAO,GAAG,KAAK;AAAA,IACnB;AAAA,EACD;AACD;AARgB;;;ACVT,IAAM,oBAAN,MAAM,0BAAsD,cAAiB;AAAA,EAC5E,KAAK,UAA4B,KAAK,kBAA0C;AACtF,WAAO,KAAK,cAAc,YAAY,OAAO,CAAmB;AAAA,EACjE;AAAA,EAEO,MAAM,UAA4B,KAAK,kBAA2C;AACxF,WAAO,KAAK,cAAc,aAAa,OAAO,CAAmB;AAAA,EAClE;AAAA,EAEO,MAA8B,OAAU,UAA4B,KAAK,kBAAuC;AACtH,WAAQ,QAAQ,KAAK,KAAK,OAAO,IAAI,KAAK,MAAM,OAAO;AAAA,EACxD;AAAA,EAEO,SAAiC,OAAU,UAA4B,KAAK,kBAAuC;AACzH,WAAQ,QAAQ,KAAK,MAAM,OAAO,IAAI,KAAK,KAAK,OAAO;AAAA,EACxD;AAAA,EAEU,OAAO,OAA4C;AAC5D,WAAO,OAAO,UAAU,YACrB,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,gBAAgB,eAAe,KAAK,iBAAiB,WAAW,gCAAgC,KAAK,CAAC;AAAA,EACzH;AACD;AAtBoF;AAA7E,IAAM,mBAAN;;;ACSP,SAAS,eACR,YACA,MACA,UACA,QACA,SACoB;AACpB,SAAO;AAAA,IACN,IAAI,OAAa;AAChB,aAAO,WAAW,MAAM,QAAQ,GAAG,MAAM,IACtC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,SAAS,WAAW,sBAAsB,OAAO,QAAQ,CAAC;AAAA,IAC3G;AAAA,EACD;AACD;AAdS;AAgBF,SAAS,aAAa,OAAa,SAA+C;AACxF,QAAM,WAAW,cAAc,MAAM,YAAY,CAAC;AAClD,SAAO,eAAe,UAAU,uBAAuB,UAAU,MAAM,QAAQ,GAAG,OAAO;AAC1F;AAHgB;AAKT,SAAS,oBAAoB,OAAa,SAA+C;AAC/F,QAAM,WAAW,eAAe,MAAM,YAAY,CAAC;AACnD,SAAO,eAAe,iBAAiB,8BAA8B,UAAU,MAAM,QAAQ,GAAG,OAAO;AACxG;AAHgB;AAKT,SAAS,gBAAgB,OAAa,SAA+C;AAC3F,QAAM,WAAW,cAAc,MAAM,YAAY,CAAC;AAClD,SAAO,eAAe,aAAa,0BAA0B,UAAU,MAAM,QAAQ,GAAG,OAAO;AAChG;AAHgB;AAKT,SAAS,uBAAuB,OAAa,SAA+C;AAClG,QAAM,WAAW,eAAe,MAAM,YAAY,CAAC;AACnD,SAAO,eAAe,oBAAoB,iCAAiC,UAAU,MAAM,QAAQ,GAAG,OAAO;AAC9G;AAHgB;AAKT,SAAS,UAAU,OAAa,SAA+C;AACrF,QAAM,WAAW,gBAAgB,MAAM,YAAY,CAAC;AACpD,SAAO,eAAe,OAAO,oBAAoB,UAAU,MAAM,QAAQ,GAAG,OAAO;AACpF;AAHgB;AAKT,SAAS,aAAa,OAAa,SAA+C;AACxF,QAAM,WAAW,gBAAgB,MAAM,YAAY,CAAC;AACpD,SAAO,eAAe,UAAU,uBAAuB,UAAU,MAAM,QAAQ,GAAG,OAAO;AAC1F;AAHgB;AAKT,SAAS,YAAY,SAA+C;AAC1E,SAAO;AAAA,IACN,IAAI,OAAa;AAChB,aAAO,OAAO,MAAM,MAAM,QAAQ,CAAC,IAChC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,sBAAsB,SAAS,WAAW,sBAAsB,OAAO,kBAAkB,CAAC;AAAA,IACrI;AAAA,EACD;AACD;AARgB;AAUT,SAAS,UAAU,SAA+C;AACxE,SAAO;AAAA,IACN,IAAI,OAAa;AAChB,aAAO,OAAO,MAAM,MAAM,QAAQ,CAAC,IAChC,OAAO,IAAI,IAAI,wBAAwB,oBAAoB,SAAS,WAAW,sBAAsB,OAAO,kBAAkB,CAAC,IAC/H,OAAO,GAAG,KAAK;AAAA,IACnB;AAAA,EACD;AACD;AARgB;;;ACzDT,IAAM,iBAAN,MAAM,uBAAsB,cAAoB;AAAA,EAC/C,SAAS,MAA8B,UAA4B,KAAK,kBAAwB;AACtG,WAAO,KAAK,cAAc,aAAa,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC;AAAA,EAChE;AAAA,EAEO,gBAAgB,MAA8B,UAA4B,KAAK,kBAAwB;AAC7G,WAAO,KAAK,cAAc,oBAAoB,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC;AAAA,EACvE;AAAA,EAEO,YAAY,MAA8B,UAA4B,KAAK,kBAAwB;AACzG,WAAO,KAAK,cAAc,gBAAgB,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC;AAAA,EACnE;AAAA,EAEO,mBAAmB,MAA8B,UAA4B,KAAK,kBAAwB;AAChH,WAAO,KAAK,cAAc,uBAAuB,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC;AAAA,EAC1E;AAAA,EAEO,MAAM,MAA8B,UAA4B,KAAK,kBAAwB;AACnG,UAAM,WAAW,IAAI,KAAK,IAAI;AAC9B,WAAO,OAAO,MAAM,SAAS,QAAQ,CAAC,IACnC,KAAK,QAAQ,OAAO,IACpB,KAAK,cAAc,UAAU,UAAU,OAAO,CAAC;AAAA,EACnD;AAAA,EAEO,SAAS,MAA8B,UAA4B,KAAK,kBAAwB;AACtG,UAAM,WAAW,IAAI,KAAK,IAAI;AAC9B,WAAO,OAAO,MAAM,SAAS,QAAQ,CAAC,IACnC,KAAK,MAAM,OAAO,IAClB,KAAK,cAAc,aAAa,UAAU,OAAO,CAAC;AAAA,EACtD;AAAA,EAEO,MAAM,UAA4B,KAAK,kBAAwB;AACrE,WAAO,KAAK,cAAc,UAAU,OAAO,CAAC;AAAA,EAC7C;AAAA,EAEO,QAAQ,UAA4B,KAAK,kBAAwB;AACvE,WAAO,KAAK,cAAc,YAAY,OAAO,CAAC;AAAA,EAC/C;AAAA,EAEU,OAAO,OAA+C;AAC/D,WAAO,iBAAiB,OACrB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,gBAAgB,YAAY,KAAK,iBAAiB,WAAW,mBAAmB,KAAK,CAAC;AAAA,EACzG;AACD;AA5CuD;AAAhD,IAAM,gBAAN;;;ACVA,IAAM,2BAAN,MAAM,iCAAmC,gBAAgB;AAAA,EAGxD,YAAY,WAAmB,SAAiB,OAAgB,UAAa;AACnF,UAAM,WAAW,SAAS,KAAK;AAC/B,SAAK,WAAW;AAAA,EACjB;AAAA,EAEgB,SAA8C;AAC7D,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,WAAW,KAAK;AAAA,MAChB,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,SAAS,KAAK;AAAA,IACf;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,YAAY,QAAQ,QAAQ,KAAK,WAAW,QAAQ;AAC1D,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,6BAA6B,SAAS,KAAK,SAAS;AAAA,IAC5E;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,EAAE;AAE3F,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AACxD,UAAM,WAAWe,SAAQ,KAAK,UAAU,UAAU,EAAE,QAAQ,OAAO,OAAO;AAC1E,UAAM,QAAQA,SAAQ,KAAK,OAAO,UAAU,EAAE,QAAQ,OAAO,OAAO;AAEpE,UAAM,SAAS,GAAG,QAAQ,QAAQ,2BAA2B,SAAS,CAAC,MAAM,SAAS;AACtF,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,gBAAgB;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,QAAQ;AACxF,UAAM,aAAa;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClF,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,aAAa;AAAA,EAAK,UAAU;AAAA,EAChE;AACD;AApCgE;AAAzD,IAAM,0BAAN;;;ACCA,IAAM,qBAAN,MAAM,2BAA6B,cAAiB;AAAA,EAGnD,YAAY,UAA0B,mBAAqC,CAAC,GAAG,cAAyC,CAAC,GAAG;AAClI,UAAM,kBAAkB,WAAW;AACnC,SAAK,WAAW;AAAA,EACjB;AAAA,EAEU,OAAO,OAAoE;AACpF,WAAO,iBAAiB,KAAK,WAC1B,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,iBAAiB,KAAK,iBAAiB,WAAW,YAAY,OAAO,KAAK,QAAQ,CAAC;AAAA,EAC9H;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,UAAU,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EACpG;AACD;AAjB2D;AAApD,IAAM,oBAAN;;;ACAA,IAAM,oBAAN,MAAM,0BAA4B,cAAiB;AAAA,EAGlD,YAAY,SAAY,mBAAqC,CAAC,GAAG,cAAyC,CAAC,GAAG;AACpH,UAAM,kBAAkB,WAAW;AACnC,SAAK,WAAW;AAAA,EACjB;AAAA,EAEU,OAAO,OAAuD;AACvE,WAAO,OAAO,GAAG,OAAO,KAAK,QAAQ,IAClC,OAAO,GAAG,KAAU,IACpB,OAAO;AAAA,MACP,IAAI,wBAAwB,gBAAgB,KAAK,iBAAiB,WAAW,gCAAgC,OAAO,KAAK,QAAQ;AAAA,IAClI;AAAA,EACH;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,UAAU,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EACpG;AACD;AAnB0D;AAAnD,IAAM,mBAAN;;;ACFA,IAAM,kBAAN,MAAM,wBAAuB,cAAqB;AAAA,EAC9C,OAAO,OAAgD;AAChE,WAAO,OAAO,IAAI,IAAI,gBAAgB,aAAa,KAAK,iBAAiB,WAAW,qCAAqC,KAAK,CAAC;AAAA,EAChI;AACD;AAJyD;AAAlD,IAAM,iBAAN;;;ACAA,IAAM,oBAAN,MAAM,0BAAyB,cAAgC;AAAA,EAC3D,OAAO,OAA2D;AAC3E,WAAO,UAAU,UAAa,UAAU,OACrC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,gBAAgB,eAAe,KAAK,iBAAiB,WAAW,8BAA8B,KAAK,CAAC;AAAA,EACvH;AACD;AANsE;AAA/D,IAAM,mBAAN;;;ACiBP,SAAS,iBACR,YACA,MACA,UACA,QACA,SACsB;AACtB,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,WAAW,OAAO,MAAM,IAC5B,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,SAAS,WAAW,wBAAwB,OAAO,QAAQ,CAAC;AAAA,IAC7G;AAAA,EACD;AACD;AAdS;AAgBF,SAAS,eAAe,OAAe,SAAiD;AAC9F,QAAM,WAAW,cAAc,KAAK;AACpC,SAAO,iBAAiB,UAAU,yBAAyB,UAAU,OAAO,OAAO;AACpF;AAHgB;AAKT,SAAS,sBAAsB,OAAe,SAAiD;AACrG,QAAM,WAAW,eAAe,KAAK;AACrC,SAAO,iBAAiB,iBAAiB,gCAAgC,UAAU,OAAO,OAAO;AAClG;AAHgB;AAKT,SAAS,kBAAkB,OAAe,SAAiD;AACjG,QAAM,WAAW,cAAc,KAAK;AACpC,SAAO,iBAAiB,aAAa,4BAA4B,UAAU,OAAO,OAAO;AAC1F;AAHgB;AAKT,SAAS,yBAAyB,OAAe,SAAiD;AACxG,QAAM,WAAW,eAAe,KAAK;AACrC,SAAO,iBAAiB,oBAAoB,mCAAmC,UAAU,OAAO,OAAO;AACxG;AAHgB;AAKT,SAAS,YAAY,OAAe,SAAiD;AAC3F,QAAM,WAAW,gBAAgB,KAAK;AACtC,SAAO,iBAAiB,OAAO,sBAAsB,UAAU,OAAO,OAAO;AAC9E;AAHgB;AAKT,SAAS,eAAe,OAAe,SAAiD;AAC9F,QAAM,WAAW,gBAAgB,KAAK;AACtC,SAAO,iBAAiB,UAAU,yBAAyB,UAAU,OAAO,OAAO;AACpF;AAHgB;AAKT,SAAS,UAAU,SAAiD;AAC1E,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,OAAO,UAAU,KAAK,IAC1B,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AAfgB;AAiBT,SAAS,cAAc,SAAiD;AAC9E,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,OAAO,cAAc,KAAK,IAC9B,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AAfgB;AAiBT,SAAS,aAAa,SAAiD;AAC7E,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,OAAO,SAAS,KAAK,IACzB,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AAfgB;AAiBT,SAAS,UAAU,SAAiD;AAC1E,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,OAAO,MAAM,KAAK,IACtB,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI,wBAAwB,yBAAyB,SAAS,WAAW,wBAAwB,OAAO,kBAAkB;AAAA,MAC3H;AAAA,IACH;AAAA,EACD;AACD;AAVgB;AAYT,SAAS,aAAa,SAAiD;AAC7E,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,OAAO,MAAM,KAAK,IACtB,OAAO;AAAA,QACP,IAAI,wBAAwB,4BAA4B,SAAS,WAAW,wBAAwB,OAAO,kBAAkB;AAAA,MAC9H,IACC,OAAO,GAAG,KAAK;AAAA,IACnB;AAAA,EACD;AACD;AAVgB;AAYT,SAAS,kBAAkB,SAAiB,SAAiD;AACnG,QAAM,WAAW,cAAc,OAAO;AACtC,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,QAAQ,YAAY,IACxB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,4BAA4B,SAAS,WAAW,2BAA2B,OAAO,QAAQ,CAAC;AAAA,IACtI;AAAA,EACD;AACD;AATgB;;;AC1HT,IAAM,mBAAN,MAAM,yBAA0C,cAAiB;AAAA,EAChE,SAAS,QAAgB,UAA4B,KAAK,kBAAwB;AACxF,WAAO,KAAK,cAAc,eAAe,QAAQ,OAAO,CAAmB;AAAA,EAC5E;AAAA,EAEO,gBAAgB,QAAgB,UAA4B,KAAK,kBAAwB;AAC/F,WAAO,KAAK,cAAc,sBAAsB,QAAQ,OAAO,CAAmB;AAAA,EACnF;AAAA,EAEO,YAAY,QAAgB,UAA4B,KAAK,kBAAwB;AAC3F,WAAO,KAAK,cAAc,kBAAkB,QAAQ,OAAO,CAAmB;AAAA,EAC/E;AAAA,EAEO,mBAAmB,QAAgB,UAA4B,KAAK,kBAAwB;AAClG,WAAO,KAAK,cAAc,yBAAyB,QAAQ,OAAO,CAAmB;AAAA,EACtF;AAAA,EAEO,MAAwB,QAAW,UAA4B,KAAK,kBAAsC;AAChH,WAAO,OAAO,MAAM,MAAM,IACtB,KAAK,cAAc,UAAU,OAAO,CAAmB,IACvD,KAAK,cAAc,YAAY,QAAQ,OAAO,CAAmB;AAAA,EACtE;AAAA,EAEO,SAAS,QAAgB,UAA4B,KAAK,kBAAwB;AACxF,WAAO,OAAO,MAAM,MAAM,IACvB,KAAK,cAAc,aAAa,OAAO,CAAmB,IAC1D,KAAK,cAAc,eAAe,QAAQ,OAAO,CAAmB;AAAA,EACxE;AAAA,EAEO,IAAI,UAA4B,KAAK,kBAAwB;AACnE,WAAO,KAAK,cAAc,UAAU,OAAO,CAAmB;AAAA,EAC/D;AAAA,EAEO,QAAQ,UAA4B,KAAK,kBAAwB;AACvE,WAAO,KAAK,cAAc,cAAc,OAAO,CAAmB;AAAA,EACnE;AAAA,EAEO,OAAO,UAA4B,KAAK,kBAAwB;AACtE,WAAO,KAAK,cAAc,aAAa,OAAO,CAAmB;AAAA,EAClE;AAAA,EAEO,SAAS,UAA4B,KAAK,kBAAwB;AACxE,WAAO,KAAK,mBAAmB,GAAG,OAAO;AAAA,EAC1C;AAAA,EAEO,SAAS,UAA4B,KAAK,kBAAwB;AACxE,WAAO,KAAK,SAAS,GAAG,OAAO;AAAA,EAChC;AAAA,EAEO,YAAY,SAAiB,UAA4B,KAAK,kBAAwB;AAC5F,WAAO,KAAK,cAAc,kBAAkB,SAAS,OAAO,CAAmB;AAAA,EAChF;AAAA,EAEO,IAAI,UAA4B,KAAK,kBAAwB;AACnE,WAAO,KAAK,UAAU,KAAK,KAA6B,OAAO;AAAA,EAChE;AAAA,EAEO,KAAK,UAA4B,KAAK,kBAAwB;AACpE,WAAO,KAAK,UAAU,KAAK,MAA8B,OAAO;AAAA,EACjE;AAAA,EAEO,MAAM,UAA4B,KAAK,kBAAwB;AACrE,WAAO,KAAK,UAAU,KAAK,OAA+B,OAAO;AAAA,EAClE;AAAA,EAEO,MAAM,UAA4B,KAAK,kBAAwB;AACrE,WAAO,KAAK,UAAU,KAAK,OAA+B,OAAO;AAAA,EAClE;AAAA,EAEO,OAAO,UAA4B,KAAK,kBAAwB;AACtE,WAAO,KAAK,UAAU,KAAK,QAAgC,OAAO;AAAA,EACnE;AAAA,EAEO,MAAM,UAA4B,KAAK,kBAAwB;AACrE,WAAO,KAAK,UAAU,KAAK,OAA+B,OAAO;AAAA,EAClE;AAAA,EAEO,KAAK,UAA4B,KAAK,kBAAwB;AACpE,WAAO,KAAK,UAAU,KAAK,MAA8B,OAAO;AAAA,EACjE;AAAA,EAEU,OAAO,OAA4C;AAC5D,WAAO,OAAO,UAAU,WACrB,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,gBAAgB,cAAc,KAAK,iBAAiB,WAAW,+BAA+B,KAAK,CAAC;AAAA,EACvH;AACD;AAtFwE;AAAjE,IAAM,kBAAN;;;ACfA,IAAM,wBAAN,MAAM,8BAA6B,UAAU;AAAA,EAG5C,YAAY,UAAuB,kBAAqC;AAC9E,UAAM,kBAAkB,WAAW,gCAAgC;AACnE,SAAK,WAAW;AAAA,EACjB;AAAA,EAEgB,SAAwC;AACvD,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,IAChB;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,WAAW,QAAQ,QAAQ,KAAK,SAAS,SAAS,GAAG,QAAQ;AACnE,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,0BAA0B,QAAQ,KAAK,SAAS;AAAA,IACxE;AAEA,UAAM,SAAS,GAAG,QAAQ,QAAQ,wBAAwB,SAAS,CAAC,MAAM,QAAQ;AAClF,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAC/B;AACD;AA1BoD;AAA7C,IAAM,uBAAN;;;ACAA,IAAM,wBAAN,MAAM,8BAA6B,UAAU;AAAA,EAI5C,YAAY,UAAuB,OAAgB,SAA4B;AACrF,UAAM,SAAS,WAAW,8BAA8B;AAExD,SAAK,WAAW;AAChB,SAAK,QAAQ;AAAA,EACd;AAAA,EAEgB,SAAuC;AACtD,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,IACb;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,WAAW,QAAQ,QAAQ,KAAK,SAAS,SAAS,GAAG,QAAQ;AACnE,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,0BAA0B,QAAQ,KAAK,SAAS;AAAA,IACxE;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,GAAG,SAAS,KAAK;AAE1G,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AACxD,UAAM,QAAQA,SAAQ,KAAK,OAAO,UAAU,EAAE,QAAQ,OAAO,OAAO;AAEpE,UAAM,SAAS,GAAG,QAAQ,QAAQ,wBAAwB,SAAS,CAAC,MAAM,QAAQ;AAClF,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,aAAa;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClF,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,UAAU;AAAA,EAC9C;AACD;AApCoD;AAA7C,IAAM,uBAAN;;;ACEA,IAAM,oBAAN,MAAM,0BAA4B,cAAiB;AAAA,EAIlD,YACN,WACA,OACA,mBAAqC,CAAC,GACtC,cAAyC,CAAC,GACzC;AACD,UAAM,kBAAkB,WAAW;AACnC,SAAK,YAAY;AACjB,SAAK,eAAe;AAAA,EACrB;AAAA,EAEgB,QACf,OACA,UAAU,KAAK,kBAC2B;AAC1C,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,mBAAmB;AACzB,UAAM,eAAe;AACrB,WAAO;AAAA,EACR;AAAA,EAEU,OAAO,OAA2C;AAC3D,WAAO,OAAO,UAAU,cACrB,OAAO,GAAG,SAAS,KAAK,YAAY,CAAC,IACrC,KAAK,UAAU,QAAQ,EAAE,KAAK;AAAA,EAClC;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,cAAc,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EACxH;AACD;AAlC0D;AAAnD,IAAM,mBAAN;;;ACHA,IAAM,iBAAN,MAAM,uBAAsB,UAAU;AAAA,EAGrC,YAAY,QAA8B,kBAAqC;AACrF,UAAM,kBAAkB,WAAW,6BAA6B;AAEhE,SAAK,SAAS;AAAA,EACf;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,mBAAmB,SAAS;AAAA,IACpD;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,GAAG,SAAS,KAAK;AAE1G,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AAExD,UAAM,SAAS,GAAG,QAAQ,QAAQ,iBAAiB,SAAS,CAAC,KAAK,QAAQ,QAAQ,KAAK,OAAO,OAAO,SAAS,GAAG,QAAQ,CAAC;AAC1H,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,SAAS,KAAK,OAClB,IAAI,CAAC,OAAOf,OAAM;AAClB,YAAM,QAAQ,QAAQ,SAASA,KAAI,GAAG,SAAS,GAAG,QAAQ;AAC1D,YAAM,OAAO,MAAM,4BAA4B,EAAE,QAAQ,GAAG,UAAU,EAAE,QAAQ,OAAO,OAAO;AAE9F,aAAO,KAAK,KAAK,IAAI,IAAI;AAAA,IAC1B,CAAC,EACA,KAAK,MAAM;AACb,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA;AAAA,EAAO,MAAM;AAAA,EAC5C;AACD;AA9B6C;AAAtC,IAAM,gBAAN;;;ACIA,IAAM,kBAAN,MAAM,wBAA0B,cAAiB;AAAA,EAGhD,YAAY,YAAyC,kBAAqC,cAAyC,CAAC,GAAG;AAC7I,UAAM,kBAAkB,WAAW;AACnC,SAAK,aAAa;AAAA,EACnB;AAAA,EAEgB,SAAS,UAA4B,KAAK,kBAAiD;AAC1G,QAAI,KAAK,WAAW,WAAW;AAC9B,aAAO,IAAI,gBAA8B,CAAC,IAAI,iBAAiB,QAAW,OAAO,CAAC,GAAG,KAAK,kBAAkB,KAAK,WAAW;AAE7H,UAAM,CAAC,SAAS,IAAI,KAAK;AACzB,QAAI,qBAAqB,kBAAkB;AAE1C,UAAI,UAAU,aAAa;AAAW,eAAO,KAAK,MAAM;AAGxD,UAAI,UAAU,aAAa,MAAM;AAChC,eAAO,IAAI;AAAA,UACV,CAAC,IAAI,iBAAiB,OAAO,GAAG,GAAG,KAAK,WAAW,MAAM,CAAC,CAAC;AAAA,UAC3D,KAAK;AAAA,UACL,KAAK;AAAA,QACN;AAAA,MACD;AAAA,IACD,WAAW,qBAAqB,kBAAkB;AAEjD,aAAO,KAAK,MAAM;AAAA,IACnB;AAEA,WAAO,IAAI,gBAAe,CAAC,IAAI,iBAAiB,QAAW,OAAO,GAAG,GAAG,KAAK,UAAU,GAAG,KAAK,gBAAgB;AAAA,EAChH;AAAA,EAEO,SAAS,UAA4B,KAAK,kBAAyD;AAGzG,QAAI,KAAK,WAAW,WAAW;AAAG,aAAO,KAAK,MAAM;AAEpD,UAAM,CAAC,SAAS,IAAI,KAAK;AACzB,QAAI,qBAAqB,kBAAkB;AAC1C,UAAI,UAAU,aAAa,QAAW;AACrC,eAAO,IAAI,gBAAe,KAAK,WAAW,MAAM,CAAC,GAAG,KAAK,kBAAkB,KAAK,WAAW;AAAA,MAC5F;AAAA,IACD,WAAW,qBAAqB,kBAAkB;AACjD,aAAO,IAAI;AAAA,QACV,CAAC,IAAI,iBAAiB,MAAM,OAAO,GAAG,GAAG,KAAK,WAAW,MAAM,CAAC,CAAC;AAAA,QACjE,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAEA,WAAO,KAAK,MAAM;AAAA,EACnB;AAAA,EAEgB,SAAS,UAA4B,KAAK,kBAA4C;AACrG,QAAI,KAAK,WAAW,WAAW,GAAG;AACjC,aAAO,IAAI,gBAAyB,CAAC,IAAI,iBAAiB,MAAM,OAAO,CAAC,GAAG,KAAK,kBAAkB,KAAK,WAAW;AAAA,IACnH;AAEA,UAAM,CAAC,SAAS,IAAI,KAAK;AACzB,QAAI,qBAAqB,kBAAkB;AAE1C,UAAI,UAAU,aAAa;AAAM,eAAO,KAAK,MAAM;AAGnD,UAAI,UAAU,aAAa,QAAW;AACrC,eAAO,IAAI;AAAA,UACV,CAAC,IAAI,iBAAiB,OAAO,GAAG,GAAG,KAAK,WAAW,MAAM,CAAC,CAAC;AAAA,UAC3D,KAAK;AAAA,UACL,KAAK;AAAA,QACN;AAAA,MACD;AAAA,IACD,WAAW,qBAAqB,kBAAkB;AAEjD,aAAO,KAAK,MAAM;AAAA,IACnB;AAEA,WAAO,IAAI,gBAAe,CAAC,IAAI,iBAAiB,MAAM,OAAO,GAAG,GAAG,KAAK,UAAU,GAAG,KAAK,gBAAgB;AAAA,EAC3G;AAAA,EAEgB,QAAQ,UAA4B,KAAK,kBAAwD;AAChH,QAAI,KAAK,WAAW,WAAW,GAAG;AACjC,aAAO,IAAI,gBAAqC,CAAC,IAAI,iBAAiB,OAAO,CAAC,GAAG,SAAS,KAAK,WAAW;AAAA,IAC3G;AAEA,UAAM,CAAC,SAAS,IAAI,KAAK;AACzB,QAAI,qBAAqB,kBAAkB;AAE1C,UAAI,UAAU,aAAa,QAAQ,UAAU,aAAa,QAAW;AACpE,eAAO,IAAI;AAAA,UACV,CAAC,IAAI,iBAAiB,OAAO,GAAG,GAAG,KAAK,WAAW,MAAM,CAAC,CAAC;AAAA,UAC3D;AAAA,UACA,KAAK;AAAA,QACN;AAAA,MACD;AAAA,IACD,WAAW,qBAAqB,kBAAkB;AAEjD,aAAO,KAAK,MAAM;AAAA,IACnB;AAEA,WAAO,IAAI,gBAAqC,CAAC,IAAI,iBAAiB,OAAO,GAAG,GAAG,KAAK,UAAU,GAAG,OAAO;AAAA,EAC7G;AAAA,EAEgB,MAAS,YAAgE;AACxF,WAAO,IAAI,gBAAsB,CAAC,GAAG,KAAK,YAAY,GAAG,UAAU,GAAG,KAAK,gBAAgB;AAAA,EAC5F;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,YAAY,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EACtG;AAAA,EAEU,OAAO,OAA4D;AAC5E,UAAM,SAAsB,CAAC;AAE7B,eAAW,aAAa,KAAK,YAAY;AACxC,YAAM,SAAS,UAAU,IAAI,KAAK;AAClC,UAAI,OAAO,KAAK;AAAG,eAAO;AAC1B,aAAO,KAAK,OAAO,KAAM;AAAA,IAC1B;AAEA,WAAO,OAAO,IAAI,IAAI,cAAc,QAAQ,KAAK,gBAAgB,CAAC;AAAA,EACnE;AACD;AA1HwD;AAAjD,IAAM,iBAAN;;;ACMA,IAAM,mBAAN,MAAM,yBAAsE,cAAiB;AAAA,EAU5F,YACN,OACA,WAAoC,gBACpC,mBAAqC,CAAC,GACtC,cAAyC,CAAC,GACzC;AACD,UAAM,kBAAkB,WAAW;AAbpC,SAAiB,OAA6B,CAAC;AAG/C,SAAiB,eAAe,oBAAI,IAAqC;AACzE,SAAiB,wBAAwB,oBAAI,IAAqC;AAClF,SAAiB,oCAAoC,oBAAI,IAAwC;AAShG,SAAK,QAAQ;AACb,SAAK,WAAW;AAEhB,YAAQ,KAAK,UAAU;AAAA,MACtB,KAAK;AACJ,aAAK,iBAAiB,CAAC,UAAU,KAAK,qBAAqB,KAAK;AAChE;AAAA,MACD,KAAK,gBAAgC;AACpC,aAAK,iBAAiB,CAAC,UAAU,KAAK,qBAAqB,KAAK;AAChE;AAAA,MACD;AAAA,MACA,KAAK;AACJ,aAAK,iBAAiB,CAAC,UAAU,KAAK,0BAA0B,KAAK;AACrE;AAAA,IACF;AAEA,UAAM,eAAe,OAAO,QAAQ,KAAK;AACzC,SAAK,OAAO,aAAa,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG;AAE3C,eAAW,CAAC,KAAK,SAAS,KAAK,cAAc;AAC5C,UAAI,qBAAqB,gBAAgB;AAExC,cAAM,CAAC,iCAAiC,IAAI,UAAU,YAAY;AAElE,YAAI,6CAA6C,kBAAkB;AAClE,eAAK,sBAAsB,IAAI,KAAK,SAAS;AAAA,QAC9C,WAAW,6CAA6C,kBAAkB;AACzE,cAAI,kCAAkC,aAAa,QAAW;AAC7D,iBAAK,sBAAsB,IAAI,KAAK,SAAS;AAAA,UAC9C,OAAO;AACN,iBAAK,aAAa,IAAI,KAAK,SAAS;AAAA,UACrC;AAAA,QACD,WAAW,qBAAqB,kBAAkB;AACjD,eAAK,kCAAkC,IAAI,KAAK,SAAS;AAAA,QAC1D,OAAO;AACN,eAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACrC;AAAA,MACD,WAAW,qBAAqB,kBAAkB;AACjD,aAAK,sBAAsB,IAAI,KAAK,SAAS;AAAA,MAC9C,WAAW,qBAAqB,kBAAkB;AACjD,YAAI,UAAU,aAAa,QAAW;AACrC,eAAK,sBAAsB,IAAI,KAAK,SAAS;AAAA,QAC9C,OAAO;AACN,eAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACrC;AAAA,MACD,WAAW,qBAAqB,kBAAkB;AACjD,aAAK,kCAAkC,IAAI,KAAK,SAAS;AAAA,MAC1D,OAAO;AACN,aAAK,aAAa,IAAI,KAAK,SAAS;AAAA,MACrC;AAAA,IACD;AAAA,EACD;AAAA,EAEO,OAAO,UAA4B,KAAK,kBAAwB;AACtE,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,OAAO,gBAAgC,SAAS,KAAK,WAAW,CAAC;AAAA,EACnH;AAAA,EAEO,OAAO,UAA4B,KAAK,kBAAwB;AACtE,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,OAAO,gBAAgC,SAAS,KAAK,WAAW,CAAC;AAAA,EACnH;AAAA,EAEO,YAAY,UAA4B,KAAK,kBAAwB;AAC3E,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,OAAO,qBAAqC,SAAS,KAAK,WAAW,CAAC;AAAA,EACxH;AAAA,EAEO,QAAQ,UAA4B,KAAK,kBAAkE;AACjH,UAAM,QAAQ,OAAO,YAAY,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,MAAM,GAAyC,EAAE,SAAS,OAAO,CAAC,CAAC,CAAC;AACvI,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,OAAO,KAAK,UAAU,SAAS,KAAK,WAAW,CAAC;AAAA,EAC7F;AAAA,EAEO,SAAS,UAA4B,KAAK,kBAAmE;AACnH,UAAM,QAAQ,OAAO;AAAA,MACpB,KAAK,KAAK,IAAI,CAAC,QAAQ;AACtB,YAAI,YAAY,KAAK,MAAM,GAAyC;AACpE,YAAI,qBAAqB;AAAgB,sBAAY,UAAU,SAAS,OAAO;AAC/E,eAAO,CAAC,KAAK,SAAS;AAAA,MACvB,CAAC;AAAA,IACF;AACA,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,OAAO,KAAK,UAAU,SAAS,KAAK,WAAW,CAAC;AAAA,EAC7F;AAAA,EAEO,OACN,QACA,UAA4B,KAAK,kBACP;AAC1B,UAAM,QAAQ,EAAE,GAAG,KAAK,OAAO,GAAI,kBAAkB,mBAAkB,OAAO,QAAQ,OAAQ;AAC9F,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,OAAO,KAAK,UAAU,SAAS,KAAK,WAAW,CAAC;AAAA,EAC7F;AAAA,EAEO,KACN,MACA,UAA4B,KAAK,kBACwB;AACzD,UAAM,QAAQ,OAAO;AAAA,MACpB,KAAK,OAAO,CAAC,QAAQ,KAAK,KAAK,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,MAAM,GAAyC,CAAC,CAAC;AAAA,IACxH;AACA,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,OAAO,KAAK,UAAU,SAAS,KAAK,WAAW,CAAC;AAAA,EAC7F;AAAA,EAEO,KACN,MACA,UAA4B,KAAK,kBACwB;AACzD,UAAM,QAAQ,OAAO;AAAA,MACpB,KAAK,KAAK,OAAO,CAAC,QAAQ,CAAC,KAAK,SAAS,GAAU,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,MAAM,GAAyC,CAAC,CAAC;AAAA,IAChI;AACA,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,OAAO,KAAK,UAAU,SAAS,KAAK,WAAW,CAAC;AAAA,EAC7F;AAAA,EAEmB,OAAO,OAAoE;AAC7F,UAAM,cAAc,OAAO;AAC3B,QAAI,gBAAgB,UAAU;AAC7B,aAAO,OAAO;AAAA,QACb,IAAI;AAAA,UACH;AAAA,UACA,KAAK,iBAAiB,WAAW,oDAAoD,WAAW;AAAA,UAChG;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,QAAI,UAAU,MAAM;AACnB,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,KAAK,iBAAiB,WAAW,qCAAqC,KAAK,CAAC;AAAA,IAClI;AAEA,QAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,KAAK,iBAAiB,WAAW,yCAAyC,KAAK,CAAC;AAAA,IACtI;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,KAAU;AAAA,IAC5B;AAEA,eAAW,aAAa,OAAO,OAAO,KAAK,KAAK,GAA2B;AAC1E,gBAAU,UAAU,KAAK,UAAU,KAAM;AAAA,IAC1C;AAEA,WAAO,KAAK,eAAe,KAAe;AAAA,EAC3C;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,OAAO,KAAK,UAAU,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EAChH;AAAA,EAEQ,qBAAqB,OAAiD;AAC7E,UAAM,SAAqC,CAAC;AAC5C,UAAM,cAAc,CAAC;AACrB,UAAM,eAAe,IAAI,IAAI,OAAO,QAAQ,KAAK,CAAyB;AAE1E,UAAM,eAAe,wBAAC,KAAc,cAAsC;AACzE,YAAM,SAAS,UAAU,IAAI,MAAM,GAAmB,CAAC;AAEvD,UAAI,OAAO,KAAK,GAAG;AAClB,oBAAY,GAAG,IAAI,OAAO;AAAA,MAC3B,OAAO;AACN,cAAM,QAAQ,OAAO;AACrB,eAAO,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,MACzB;AAAA,IACD,GATqB;AAWrB,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,cAAc;AACjD,UAAI,aAAa,OAAO,GAAG,GAAG;AAC7B,qBAAa,KAAK,SAAS;AAAA,MAC5B,OAAO;AACN,eAAO,KAAK,CAAC,KAAK,IAAI,qBAAqB,KAAK,KAAK,gBAAgB,CAAC,CAAC;AAAA,MACxE;AAAA,IACD;AAGA,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,mCAAmC;AACtE,mBAAa,OAAO,GAAG;AACvB,mBAAa,KAAK,SAAS;AAAA,IAC5B;AAGA,QAAI,aAAa,SAAS,GAAG;AAC5B,aAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,QAAQ,KAAK,gBAAgB,CAAC;AAAA,IACvE;AAIA,UAAM,uCAAuC,KAAK,sBAAsB,OAAO,aAAa;AAE5F,QAAI,sCAAsC;AACzC,iBAAW,CAAC,GAAG,KAAK,cAAc;AACjC,cAAM,YAAY,KAAK,sBAAsB,IAAI,GAAG;AAEpD,YAAI,WAAW;AACd,uBAAa,KAAK,SAAS;AAAA,QAC5B;AAAA,MACD;AAAA,IACD,OAAO;AACN,iBAAW,CAAC,KAAK,SAAS,KAAK,KAAK,uBAAuB;AAC1D,YAAI,aAAa,OAAO,GAAG,GAAG;AAC7B,uBAAa,KAAK,SAAS;AAAA,QAC5B;AAAA,MACD;AAAA,IACD;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,QAAQ,KAAK,gBAAgB,CAAC;AAAA,EACvE;AAAA,EAEQ,qBAAqB,OAAiD;AAC7E,UAAM,SAAqC,CAAC;AAC5C,UAAM,cAAc,CAAC;AACrB,UAAM,eAAe,IAAI,IAAI,OAAO,QAAQ,KAAK,CAAyB;AAE1E,UAAM,eAAe,wBAAC,KAAc,cAAsC;AACzE,YAAM,SAAS,UAAU,IAAI,MAAM,GAAmB,CAAC;AAEvD,UAAI,OAAO,KAAK,GAAG;AAClB,oBAAY,GAAG,IAAI,OAAO;AAAA,MAC3B,OAAO;AACN,cAAM,QAAQ,OAAO;AACrB,eAAO,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,MACzB;AAAA,IACD,GATqB;AAWrB,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,cAAc;AACjD,UAAI,aAAa,OAAO,GAAG,GAAG;AAC7B,qBAAa,KAAK,SAAS;AAAA,MAC5B,OAAO;AACN,eAAO,KAAK,CAAC,KAAK,IAAI,qBAAqB,KAAK,KAAK,gBAAgB,CAAC,CAAC;AAAA,MACxE;AAAA,IACD;AAGA,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,mCAAmC;AACtE,mBAAa,OAAO,GAAG;AACvB,mBAAa,KAAK,SAAS;AAAA,IAC5B;AAEA,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,uBAAuB;AAG1D,UAAI,aAAa,SAAS,GAAG;AAC5B;AAAA,MACD;AAEA,UAAI,aAAa,OAAO,GAAG,GAAG;AAC7B,qBAAa,KAAK,SAAS;AAAA,MAC5B;AAAA,IACD;AAEA,QAAI,aAAa,SAAS,GAAG;AAC5B,iBAAW,CAAC,KAAKwC,MAAK,KAAK,aAAa,QAAQ,GAAG;AAClD,eAAO,KAAK,CAAC,KAAK,IAAI,qBAAqB,KAAKA,QAAO,KAAK,gBAAgB,CAAC,CAAC;AAAA,MAC/E;AAAA,IACD;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,QAAQ,KAAK,gBAAgB,CAAC;AAAA,EACvE;AAAA,EAEQ,0BAA0B,OAAiD;AAClF,UAAM,SAAS,KAAK,qBAAqB,KAAK;AAC9C,WAAO,OAAO,MAAM,IAAI,SAAS,OAAO,GAAG,EAAE,GAAG,OAAO,GAAG,OAAO,MAAM,CAAM;AAAA,EAC9E;AACD;AAxRoG;AAA7F,IAAM,kBAAN;;;ACVA,IAAM,wBAAN,MAAM,8BAAsD,cAAiB;AAAA,EACzE,OAAO,OAA4C;AAC5D,WAAO,OAAO,GAAG,KAAU;AAAA,EAC5B;AACD;AAJoF;AAA7E,IAAM,uBAAN;;;ACIA,IAAM,mBAAN,MAAM,yBAA2B,cAAiC;AAAA,EAGjE,YACN,WACA,mBAAqC,CAAC,GACtC,cAAyD,CAAC,GACzD;AACD,UAAM,kBAAkB,WAAW;AACnC,SAAK,YAAY;AAAA,EAClB;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EACrG;AAAA,EAEU,OAAO,OAAoF;AACpG,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,KAAK,iBAAiB,WAAW,sBAAsB,KAAK,CAAC;AAAA,IACnH;AAEA,QAAI,UAAU,MAAM;AACnB,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,KAAK,iBAAiB,WAAW,qCAAqC,KAAK,CAAC;AAAA,IAClI;AAEA,QAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,KAAK,iBAAiB,WAAW,yCAAyC,KAAK,CAAC;AAAA,IACtI;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,KAA0B;AAAA,IAC5C;AAEA,UAAM,SAAgC,CAAC;AACvC,UAAM,cAAiC,CAAC;AAExC,eAAW,CAAC,KAAK,GAAG,KAAK,OAAO,QAAQ,KAAM,GAAG;AAChD,YAAM,SAAS,KAAK,UAAU,IAAI,GAAG;AACrC,UAAI,OAAO,KAAK;AAAG,oBAAY,GAAG,IAAI,OAAO;AAAA;AACxC,eAAO,KAAK,CAAC,KAAK,OAAO,KAAM,CAAC;AAAA,IACtC;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,QAAQ,KAAK,gBAAgB,CAAC;AAAA,EACvE;AACD;AA9CyE;AAAlE,IAAM,kBAAN;;;ACAA,IAAM,gBAAN,MAAM,sBAAwB,cAAsB;AAAA,EAGnD,YAAY,WAA6B,kBAAqC,cAA8C,CAAC,GAAG;AACtI,UAAM,kBAAkB,WAAW;AACnC,SAAK,YAAY;AAAA,EAClB;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EACrG;AAAA,EAEU,OAAO,QAAkE;AAClF,QAAI,EAAE,kBAAkB,MAAM;AAC7B,aAAO,OAAO,IAAI,IAAI,gBAAgB,YAAY,KAAK,iBAAiB,WAAW,kBAAkB,MAAM,CAAC;AAAA,IAC7G;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,MAAM;AAAA,IACxB;AAEA,UAAM,SAAsB,CAAC;AAC7B,UAAM,cAAc,oBAAI,IAAO;AAE/B,eAAW,SAAS,QAAQ;AAC3B,YAAM,SAAS,KAAK,UAAU,IAAI,KAAK;AACvC,UAAI,OAAO,KAAK;AAAG,oBAAY,IAAI,OAAO,KAAK;AAAA;AAC1C,eAAO,KAAK,OAAO,KAAM;AAAA,IAC/B;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,cAAc,QAAQ,KAAK,gBAAgB,CAAC;AAAA,EAC/D;AACD;AAlC2D;AAApD,IAAM,eAAN;;;ACHP,IAAM,eAAe;AAqBd,SAAS,cAAc,OAAwB;AAIrD,MAAI,CAAC;AAAO,WAAO;AAGnB,QAAM,UAAU,MAAM,QAAQ,GAAG;AAKjC,MAAI,YAAY;AAAI,WAAO;AAO3B,MAAI,UAAU;AAAI,WAAO;AAEzB,QAAM,cAAc,UAAU;AAK9B,MAAI,MAAM,SAAS,KAAK,WAAW;AAAG,WAAO;AAO7C,MAAI,MAAM,SAAS,cAAc;AAAK,WAAO;AAG7C,MAAI,WAAW,MAAM,QAAQ,KAAK,WAAW;AAM7C,MAAI,aAAa;AAAI,WAAO;AAgB5B,MAAI,eAAe;AACnB,KAAG;AACF,QAAI,WAAW,eAAe;AAAI,aAAO;AAEzC,mBAAe,WAAW;AAAA,EAC3B,UAAU,WAAW,MAAM,QAAQ,KAAK,YAAY,OAAO;AAI3D,MAAI,MAAM,SAAS,eAAe;AAAI,WAAO;AAY7C,SAAO,aAAa,KAAK,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,oBAAoB,MAAM,MAAM,WAAW,CAAC;AAClG;AAhFgB;AAkFhB,SAAS,oBAAoB,QAAyB;AACrD,MAAI;AACH,WAAO,IAAI,IAAI,UAAU,MAAM,EAAE,EAAE,aAAa;AAAA,EACjD,QAAQ;AACP,WAAO;AAAA,EACR;AACD;AANS;;;ACvGT,IAAM,QAAQ;AACd,IAAM,QAAQ,IAAI,KAAK,UAAU,KAAK;AACtC,IAAM,UAAU,IAAI,OAAO,IAAI,KAAK,GAAG;AAGvC,IAAM,QAAQ;AACd,IAAM,UAAU,IAAI;AAAA,EACnB,QACO,KAAK,WAAW,KAAK,UACrB,KAAK,WAAW,KAAK,KAAK,KAAK,UAC/B,KAAK,YAAY,KAAK,MAAM,KAAK,gBACjC,KAAK,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,gBACjD,KAAK,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,gBACjD,KAAK,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,gBACjD,KAAK,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,sBAC3C,KAAK,UAAU,KAAK,QAAQ,KAAK;AAE/C;AAEO,SAAS,OAAOrC,IAAoB;AAC1C,SAAO,QAAQ,KAAKA,EAAC;AACtB;AAFgB;AAIT,SAAS,OAAOA,IAAoB;AAC1C,SAAO,QAAQ,KAAKA,EAAC;AACtB;AAFgB;AAIT,SAAS,KAAKA,IAAmB;AACvC,MAAI,OAAOA,EAAC;AAAG,WAAO;AACtB,MAAI,OAAOA,EAAC;AAAG,WAAO;AACtB,SAAO;AACR;AAJgB;;;AChCT,IAAM,mBAAmB;AAEzB,SAAS,oBAAoB,OAAe;AAClD,SAAO,iBAAiB,KAAK,KAAK;AACnC;AAFgB;;;ACGT,IAAM,wCAAN,MAAM,8CAA0D,oBAAuB;AAAA,EAGtF,YAAY,YAAkC,SAAiB,OAAU,UAA6B;AAC5G,UAAM,YAAY,SAAS,KAAK;AAChC,SAAK,WAAW;AAAA,EACjB;AAAA,EAEgB,SAA2D;AAC1E,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,YAAY,KAAK;AAAA,MACjB,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,IAChB;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,aAAa,QAAQ,QAAQ,KAAK,YAAY,QAAQ;AAC5D,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,0CAA0C,UAAU,KAAK,SAAS;AAAA,IAC1F;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,EAAE;AAE3F,UAAM,eAAe,QAAQ,QAAQ,KAAK,WAAW;AACrD,UAAM,UAAU;AAAA,IAAO,YAAY;AACnC,UAAM,QAAQY,SAAQ,KAAK,OAAO,UAAU,EAAE,QAAQ,OAAO,OAAO;AAEpE,UAAM,SAAS,GAAG,QAAQ,QAAQ,wCAAwC,SAAS,CAAC,MAAM,UAAU;AACpG,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AAEtD,UAAM,kBAAkB;AAAA,IAAO,YAAY;AAC3C,UAAM,gBAAgB;AAAA,IAAO,QAAQ,QAAQ,kCAAkC,QAAQ,CAAC,GAAG,eAAe,GAAG,KAAK,SAChH,IAAI,CAAC,aAAa,QAAQ,QAAQ,UAAU,SAAS,CAAC,EACtD,KAAK,eAAe,CAAC;AACvB,UAAM,aAAa;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClF,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,aAAa;AAAA,EAAK,UAAU;AAAA,EAChE;AACD;AAxC8F;AAAvF,IAAM,uCAAN;;;ACLA,SAAS,mBAAwD,KAAqC;AAC5G,UAAQ,IAAI,QAAQ;AAAA,IACnB,KAAK;AACJ,aAAO,MAAM;AAAA,IACd,KAAK;AACJ,aAAO,IAAI,CAAC;AAAA,IACb,KAAK,GAAG;AACP,YAAM,CAAC,KAAK,GAAG,IAAI;AACnB,aAAO,IAAI,WAAW,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG,MAAM;AAAA,IACtD;AAAA,IACA,SAAS;AACR,aAAO,IAAI,WAAW;AACrB,mBAAW,MAAM,KAAK;AACrB,gBAAM,SAAS,GAAG,GAAG,MAAM;AAC3B,cAAI;AAAQ,mBAAO;AAAA,QACpB;AAEA,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AACD;AArBgB;;;ACaT,SAAS,oBAAoB,SAAsB,kBAAqC;AAC9F,QAAM,MAA0F,CAAC;AAEjG,MAAI,SAAS,kBAAkB;AAAQ,QAAI,KAAK,mBAAmB,QAAQ,kBAAkB,gBAAgB,CAAC;AAC9G,MAAI,SAAS,gBAAgB;AAAQ,QAAI,KAAK,iBAAiB,QAAQ,gBAAgB,gBAAgB,CAAC;AAExG,SAAO,gBAAgB,GAAG,GAAG;AAC9B;AAPgB;AAShB,SAAS,mBAAmB,kBAAoC,SAA4B;AAC3F,SAAO,CAAC,OAAe,QACtB,iBAAiB,SAAS,IAAI,QAA0B,IACrD,OACA,IAAI,qCAAqC,oBAAoB,SAAS,WAAW,wBAAwB,OAAO,gBAAgB;AACrI;AALS;AAOT,SAAS,iBAAiB,gBAAgC,SAA4B;AACrF,SAAO,CAAC,OAAe,QACtB,eAAe,SAAS,IAAI,QAAwB,IACjD,OACA,IAAI,qCAAqC,oBAAoB,SAAS,WAAW,sBAAsB,OAAO,cAAc;AACjI;AALS;;;ACOT,SAAS,uBACR,YACA,MACA,UACA,QACA,SACsB;AACtB,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,WAAW,MAAM,QAAQ,MAAM,IACnC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,SAAS,WAAW,yBAAyB,OAAO,QAAQ,CAAC;AAAA,IAC9G;AAAA,EACD;AACD;AAdS;AAgBF,SAAS,qBAAqB,QAAgB,SAAiD;AACrG,QAAM,WAAW,qBAAqB,MAAM;AAC5C,SAAO,uBAAuB,UAAU,+BAA+B,UAAU,QAAQ,OAAO;AACjG;AAHgB;AAKT,SAAS,4BAA4B,QAAgB,SAAiD;AAC5G,QAAM,WAAW,sBAAsB,MAAM;AAC7C,SAAO,uBAAuB,iBAAiB,sCAAsC,UAAU,QAAQ,OAAO;AAC/G;AAHgB;AAKT,SAAS,wBAAwB,QAAgB,SAAiD;AACxG,QAAM,WAAW,qBAAqB,MAAM;AAC5C,SAAO,uBAAuB,aAAa,kCAAkC,UAAU,QAAQ,OAAO;AACvG;AAHgB;AAKT,SAAS,+BAA+B,QAAgB,SAAiD;AAC/G,QAAM,WAAW,sBAAsB,MAAM;AAC7C,SAAO,uBAAuB,oBAAoB,yCAAyC,UAAU,QAAQ,OAAO;AACrH;AAHgB;AAKT,SAAS,kBAAkB,QAAgB,SAAiD;AAClG,QAAM,WAAW,uBAAuB,MAAM;AAC9C,SAAO,uBAAuB,OAAO,4BAA4B,UAAU,QAAQ,OAAO;AAC3F;AAHgB;AAKT,SAAS,qBAAqB,QAAgB,SAAiD;AACrG,QAAM,WAAW,uBAAuB,MAAM;AAC9C,SAAO,uBAAuB,UAAU,+BAA+B,UAAU,QAAQ,OAAO;AACjG;AAHgB;AAKT,SAAS,YAAY,SAAiD;AAC5E,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,cAAc,KAAK,IACvB,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AAfgB;AAiBhB,SAAS,qBAAqB,MAA4B,UAAkB,OAAe,SAAiD;AAC3I,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,MAAM,KAAK,KAAK,IACpB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,SAAS,WAAW,yBAAyB,OAAO,QAAQ,CAAC;AAAA,IAC9G;AAAA,EACD;AACD;AARS;AAUF,SAAS,UAAU,SAAsB,kBAA0D;AACzG,QAAM,cAAc,oBAAoB,SAAS,gBAAgB;AACjE,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,UAAI;AACJ,UAAI;AACH,cAAM,IAAI,IAAI,KAAK;AAAA,MACpB,QAAQ;AACP,eAAO,OAAO;AAAA,UACb,IAAI,wBAAwB,oBAAoB,kBAAkB,WAAW,eAAe,OAAO,yBAAyB;AAAA,QAC7H;AAAA,MACD;AAEA,YAAM,oBAAoB,YAAY,OAAO,GAAG;AAChD,UAAI,sBAAsB;AAAM,eAAO,OAAO,GAAG,KAAK;AACtD,aAAO,OAAO,IAAI,iBAAiB;AAAA,IACpC;AAAA,EACD;AACD;AAlBgB;AAoBT,SAAS,SAAS,SAAiB,SAAiD;AAC1F,QAAM,YAAY,UAAW,IAAI,OAAO,KAAe;AACvD,QAAM,cAAc,YAAY,IAAI,SAAS,YAAY,IAAI,SAAS;AAEtE,QAAM,OAAO,gBAAgB,SAAS;AACtC,QAAM,UAAU,aAAa,SAAS;AACtC,QAAM,WAAW,uBAAuB,SAAS;AACjD,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,YAAY,KAAK,IACrB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,SAAS,WAAW,SAAS,OAAO,QAAQ,CAAC;AAAA,IAC9F;AAAA,EACD;AACD;AAdgB;AAgBT,SAAS,YAAY,OAAe,SAA4B;AACtE,SAAO,qBAAqB,sBAAsB,YAAY,KAAK,8BAA8B,OAAO,OAAO;AAChH;AAFgB;AAIT,SAAS,WAAW,EAAE,UAAU,GAAG,WAAW,MAAM,IAAuB,CAAC,GAAG,SAA4B;AACjH,wBAAY;AACZ,QAAM,QAAQ,IAAI;AAAA,IACjB,gCAAgC,OAAO,8CACtC,WAAW,0CAA0C,EACtD;AAAA,IACA;AAAA,EACD;AACA,QAAM,WAAW,yBAAyB,OAAO,YAAY,WAAW,IAAI,OAAO,KAAK,gBAAgB,OAAO,EAAE;AACjH,SAAO,qBAAqB,qBAAqB,UAAU,OAAO,OAAO;AAC1E;AAVgB;AAYT,SAAS,WAAW,SAAiD;AAC3E,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,YAAM,OAAO,KAAK,MAAM,KAAK;AAE7B,aAAO,OAAO,MAAM,IAAI,IACrB,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD,IACC,OAAO,GAAG,KAAK;AAAA,IACnB;AAAA,EACD;AACD;AAjBgB;AAmBT,SAAS,YAAY,SAAiD;AAC5E,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,oBAAoB,KAAK,IAC7B,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AAfgB;;;AC7JT,IAAM,mBAAN,MAAM,yBAA0C,cAAiB;AAAA,EAChE,eAAe,QAAgB,UAA4B,KAAK,kBAAwB;AAC9F,WAAO,KAAK,cAAc,qBAAqB,QAAQ,OAAO,CAAmB;AAAA,EAClF;AAAA,EAEO,sBAAsB,QAAgB,UAA4B,KAAK,kBAAwB;AACrG,WAAO,KAAK,cAAc,4BAA4B,QAAQ,OAAO,CAAmB;AAAA,EACzF;AAAA,EAEO,kBAAkB,QAAgB,UAA4B,KAAK,kBAAwB;AACjG,WAAO,KAAK,cAAc,wBAAwB,QAAQ,OAAO,CAAmB;AAAA,EACrF;AAAA,EAEO,yBAAyB,QAAgB,UAA4B,KAAK,kBAAwB;AACxG,WAAO,KAAK,cAAc,+BAA+B,QAAQ,OAAO,CAAmB;AAAA,EAC5F;AAAA,EAEO,YAAY,QAAgB,UAA4B,KAAK,kBAAwB;AAC3F,WAAO,KAAK,cAAc,kBAAkB,QAAQ,OAAO,CAAmB;AAAA,EAC/E;AAAA,EAEO,eAAe,QAAgB,UAA4B,KAAK,kBAAwB;AAC9F,WAAO,KAAK,cAAc,qBAAqB,QAAQ,OAAO,CAAmB;AAAA,EAClF;AAAA,EAEO,MAAM,UAA4B,KAAK,kBAAwB;AACrE,WAAO,KAAK,cAAc,YAAY,OAAO,CAAmB;AAAA,EACjE;AAAA,EAIO,IAAI,SAAyC,mBAAqC,KAAK,kBAAwB;AACrH,UAAM,aAAa,KAAK,aAAa,OAAO;AAE5C,QAAI,YAAY;AACf,aAAO,KAAK,cAAc,UAAU,SAAS,gBAAgB,CAAmB;AAAA,IACjF;AAEA,WAAO,KAAK,cAAc,UAAU,QAAW,gBAAgB,CAAmB;AAAA,EACnF;AAAA,EAIO,KAAK,SAAgD,mBAAqC,KAAK,kBAAwB;AAC7H,UAAM,oBAAoB,KAAK,oBAAoB,OAAO;AAE1D,QAAI,mBAAmB;AACtB,aAAO,KAAK,cAAc,WAAW,SAAS,gBAAgB,CAAmB;AAAA,IAClF;AAEA,WAAO,KAAK,cAAc,WAAW,QAAW,gBAAgB,CAAmB;AAAA,EACpF;AAAA,EAEO,MAAM,OAAe,UAA4B,KAAK,kBAAwB;AACpF,WAAO,KAAK,cAAc,YAAY,OAAO,OAAO,CAAmB;AAAA,EACxE;AAAA,EAEO,KAAK,UAA4B,KAAK,kBAAkB;AAC9D,WAAO,KAAK,cAAc,WAAW,OAAO,CAAmB;AAAA,EAChE;AAAA,EAEO,KAAK,UAA4B,KAAK,kBAAwB;AACpE,WAAO,KAAK,GAAG,GAAG,OAAO;AAAA,EAC1B;AAAA,EAEO,KAAK,UAA4B,KAAK,kBAAwB;AACpE,WAAO,KAAK,GAAG,GAAG,OAAO;AAAA,EAC1B;AAAA,EAEO,GAAG,SAAiB,UAA4B,KAAK,kBAAwB;AACnF,WAAO,KAAK,cAAc,SAAS,SAAS,OAAO,CAAmB;AAAA,EACvE;AAAA,EAEO,MAAM,UAA4B,KAAK,kBAAwB;AACrE,WAAO,KAAK,cAAc,YAAY,OAAO,CAAmB;AAAA,EACjE;AAAA,EAEU,OAAO,OAA4C;AAC5D,WAAO,OAAO,UAAU,WACrB,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,gBAAgB,cAAc,KAAK,iBAAiB,WAAW,+BAA+B,KAAK,CAAC;AAAA,EACvH;AAAA,EAEQ,aAAa,SAAgE;AACpF,WAAQ,SAA8B,YAAY;AAAA,EACnD;AAAA,EAEQ,oBAAoB,SAA8E;AACzG,WAAQ,SAA8B,YAAY;AAAA,EACnD;AACD;AA1FwE;AAAjE,IAAM,kBAAN;;;ACfA,IAAM,kBAAN,MAAM,wBAAwC,cAAsB;AAAA,EAGnE,YACN,YACA,mBAAqC,CAAC,GACtC,cAA8C,CAAC,GAC9C;AACD,UAAM,kBAAkB,WAAW;AAPpC,SAAiB,aAAsC,CAAC;AAQvD,SAAK,aAAa;AAAA,EACnB;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,YAAY,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EACtG;AAAA,EAEU,OAAO,QAA0E;AAC1F,QAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC3B,aAAO,OAAO,IAAI,IAAI,gBAAgB,cAAc,KAAK,iBAAiB,WAAW,qBAAqB,MAAM,CAAC;AAAA,IAClH;AAEA,QAAI,OAAO,WAAW,KAAK,WAAW,QAAQ;AAC7C,aAAO,OAAO;AAAA,QACb,IAAI,gBAAgB,cAAc,KAAK,iBAAiB,WAAW,+BAA+B,KAAK,WAAW,MAAM,IAAI,MAAM;AAAA,MACnI;AAAA,IACD;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,MAAgB;AAAA,IAClC;AAEA,UAAM,SAAgC,CAAC;AACvC,UAAM,cAAiB,CAAC;AAExB,aAASf,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACvC,YAAM,SAAS,KAAK,WAAWA,EAAC,EAAE,IAAI,OAAOA,EAAC,CAAC;AAC/C,UAAI,OAAO,KAAK;AAAG,oBAAY,KAAK,OAAO,KAAK;AAAA;AAC3C,eAAO,KAAK,CAACA,IAAG,OAAO,KAAM,CAAC;AAAA,IACpC;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,QAAQ,KAAK,gBAAgB,CAAC;AAAA,EACvE;AACD;AA5C2E;AAApE,IAAM,iBAAN;;;ACAA,IAAM,gBAAN,MAAM,sBAA2B,cAAyB;AAAA,EAIzD,YACN,cACA,gBACA,mBAAqC,CAAC,GACtC,cAAiD,CAAC,GACjD;AACD,UAAM,kBAAkB,WAAW;AACnC,SAAK,eAAe;AACpB,SAAK,iBAAiB;AAAA,EACvB;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,cAAc,KAAK,gBAAgB,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EAC7H;AAAA,EAEU,OAAO,OAA4E;AAC5F,QAAI,EAAE,iBAAiB,MAAM;AAC5B,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,KAAK,iBAAiB,WAAW,kBAAkB,KAAK,CAAC;AAAA,IAC/G;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,KAAK;AAAA,IACvB;AAEA,UAAM,SAAgC,CAAC;AACvC,UAAM,cAAc,oBAAI,IAAU;AAElC,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM,QAAQ,GAAG;AACzC,YAAM,YAAY,KAAK,aAAa,IAAI,GAAG;AAC3C,YAAM,cAAc,KAAK,eAAe,IAAI,GAAG;AAC/C,YAAM,EAAE,OAAO,IAAI;AACnB,UAAI,UAAU,MAAM;AAAG,eAAO,KAAK,CAAC,KAAK,UAAU,KAAK,CAAC;AACzD,UAAI,YAAY,MAAM;AAAG,eAAO,KAAK,CAAC,KAAK,YAAY,KAAK,CAAC;AAC7D,UAAI,OAAO,WAAW;AAAQ,oBAAY,IAAI,UAAU,OAAQ,YAAY,KAAM;AAAA,IACnF;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,QAAQ,KAAK,gBAAgB,CAAC;AAAA,EACvE;AACD;AA5CiE;AAA1D,IAAM,eAAN;;;ACHA,IAAM,iBAAN,MAAM,uBAAuE,cAAiB;AAAA,EAG7F,YAAY,WAAkC,mBAAqC,CAAC,GAAG,cAAyC,CAAC,GAAG;AAC1I,UAAM,kBAAkB,WAAW;AACnC,SAAK,YAAY;AAAA,EAClB;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EACrG;AAAA,EAEU,OAAO,QAA4C;AAC5D,WAAO,KAAK,UAAU,MAAM,EAAE,IAAI,MAAM;AAAA,EACzC;AACD;AAfqG;AAA9F,IAAM,gBAAN;;;ACAA,IAAM,yBAAN,MAAM,+BAA8B,UAAU;AAAA,EAK7C,YACN,OACA,MACA,cACA,kBACC;AACD,UAAM,kBAAkB,WAAW,4DAA4D;AAE/F,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACrB;AAAA,EAEgB,SAAyC;AACxD,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,cAAc,CAAC,GAAG,KAAK,aAAa,QAAQ,CAAC;AAAA,IAC9C;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,QAAQ,QAAQ,QAAQ,KAAK,MAAM,SAAS,GAAG,QAAQ;AAC7D,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,2BAA2B,KAAK,KAAK,SAAS;AAAA,IACtE;AAEA,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AACxD,UAAM,QAAQ,KAAK,SACjB,IAAI,CAAC,QAAQ;AACb,YAAM,YAAY,KAAK,aAAa,IAAI,GAAG;AAC3C,aAAO,GAAG,QAAQ,QAAQ,KAAK,QAAQ,CAAC,OAAO,QAAQ;AAAA,QACtD,UAAU,SAAS;AAAA,QACnB,OAAO,cAAc,WAAW,WAAW;AAAA,MAC5C,CAAC;AAAA,IACF,CAAC,EACA,KAAK,OAAO;AAEd,UAAM,SAAS,GAAG,QAAQ,QAAQ,yBAAyB,SAAS,CAAC,MAAM,KAAK;AAChF,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,aAAa,GAAG,OAAO,GAAG,KAAK;AACrC,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,UAAU;AAAA,EAC9C;AACD;AAlDqD;AAA9C,IAAM,wBAAN;;;ACCA,IAAM,uBAAN,MAAM,6BAAsD,cAA0B;AAAA,EAMrF,YAAY,WAAc,mBAAqC,CAAC,GAAG;AACzE,UAAM,gBAAgB;AALvB,SAAgB,qBAA8B;AAE9C,SAAiB,cAAc,oBAAI,IAAiC;AAInE,SAAK,YAAY;AAEjB,SAAK,WAAW,OAAO,KAAK,SAAS,EAAE,OAAO,CAAC,QAAQ;AACtD,aAAO,OAAO,UAAU,UAAU,GAAG,CAAC,MAAM;AAAA,IAC7C,CAAC;AAED,eAAW,OAAO,KAAK,UAAU;AAChC,YAAM,YAAY,UAAU,GAAG;AAE/B,WAAK,YAAY,IAAI,KAAK,SAAS;AACnC,WAAK,YAAY,IAAI,WAAW,SAAS;AAEzC,UAAI,OAAO,cAAc,UAAU;AAClC,aAAK,qBAAqB;AAC1B,aAAK,YAAY,IAAI,GAAG,SAAS,IAAI,SAAS;AAAA,MAC/C;AAAA,IACD;AAAA,EACD;AAAA,EAEmB,OAAO,OAA6E;AACtG,UAAM,cAAc,OAAO;AAE3B,QAAI,gBAAgB,UAAU;AAC7B,UAAI,CAAC,KAAK,oBAAoB;AAC7B,eAAO,OAAO;AAAA,UACb,IAAI,gBAAgB,mBAAmB,KAAK,iBAAiB,WAAW,qCAAqC,KAAK;AAAA,QACnH;AAAA,MACD;AAAA,IACD,WAAW,gBAAgB,UAAU;AAEpC,aAAO,OAAO;AAAA,QACb,IAAI,gBAAgB,mBAAmB,KAAK,iBAAiB,WAAW,+CAA+C,KAAK;AAAA,MAC7H;AAAA,IACD;AAEA,UAAM,SAAS;AAEf,UAAM,oBAAoB,KAAK,YAAY,IAAI,MAAM;AAErD,WAAO,OAAO,sBAAsB,cACjC,OAAO,IAAI,IAAI,sBAAsB,QAAQ,KAAK,UAAU,KAAK,aAAa,KAAK,gBAAgB,CAAC,IACpG,OAAO,GAAG,iBAAiB;AAAA,EAC/B;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,gBAAgB,CAAC;AAAA,EACnF;AACD;AAvD6F;AAAtF,IAAM,sBAAN;;;ACYP,SAAS,+BACR,YACA,MACA,UACA,QACA,SACiB;AACjB,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,WAAW,MAAM,YAAY,MAAM,IACvC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,SAAS,WAAW,mCAAmC,OAAO,QAAQ,CAAC;AAAA,IACxH;AAAA,EACD;AACD;AAdS;AAgBF,SAAS,6BAAmD,OAAe,SAA4C;AAC7H,QAAM,WAAW,yBAAyB,KAAK;AAC/C,SAAO,+BAA+B,UAAU,wCAAwC,UAAU,OAAO,OAAO;AACjH;AAHgB;AAKT,SAAS,oCAA0D,OAAe,SAA4C;AACpI,QAAM,WAAW,0BAA0B,KAAK;AAChD,SAAO,+BAA+B,iBAAiB,+CAA+C,UAAU,OAAO,OAAO;AAC/H;AAHgB;AAKT,SAAS,gCAAsD,OAAe,SAA4C;AAChI,QAAM,WAAW,yBAAyB,KAAK;AAC/C,SAAO,+BAA+B,aAAa,2CAA2C,UAAU,OAAO,OAAO;AACvH;AAHgB;AAKT,SAAS,uCAA6D,OAAe,SAA4C;AACvI,QAAM,WAAW,0BAA0B,KAAK;AAChD,SAAO,+BAA+B,oBAAoB,kDAAkD,UAAU,OAAO,OAAO;AACrI;AAHgB;AAKT,SAAS,0BAAgD,OAAe,SAA4C;AAC1H,QAAM,WAAW,2BAA2B,KAAK;AACjD,SAAO,+BAA+B,OAAO,qCAAqC,UAAU,OAAO,OAAO;AAC3G;AAHgB;AAKT,SAAS,6BAAmD,OAAe,SAA4C;AAC7H,QAAM,WAAW,2BAA2B,KAAK;AACjD,SAAO,+BAA+B,UAAU,wCAAwC,UAAU,OAAO,OAAO;AACjH;AAHgB;AAKT,SAAS,0BAAgD,OAAe,WAAmB,SAA4C;AAC7I,QAAM,WAAW,0BAA0B,KAAK,6BAA6B,SAAS;AACtF,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,cAAc,SAAS,MAAM,aAAa,YACpD,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AAhBgB;AAkBT,SAAS,mCAAyD,OAAe,KAAa,SAA4B;AAChI,QAAM,WAAW,0BAA0B,KAAK,8BAA8B,GAAG;AACjF,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,cAAc,SAAS,MAAM,cAAc,MACrD,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AAhBgB;AAkBT,SAAS,mCACf,YACA,WACA,SACiB;AACjB,QAAM,WAAW,yBAAyB,UAAU,6BAA6B,SAAS;AAC1F,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,aAAa,cAAc,MAAM,aAAa,YACxD,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AApBgB;AAsBhB,SAAS,2BACR,YACA,MACA,UACA,QACA,SACiB;AACjB,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,WAAW,MAAM,QAAQ,MAAM,IACnC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,SAAS,WAAW,8BAA8B,OAAO,QAAQ,CAAC;AAAA,IACnH;AAAA,EACD;AACD;AAdS;AAgBF,SAAS,yBAA+C,OAAe,SAA4C;AACzH,QAAM,WAAW,qBAAqB,KAAK;AAC3C,SAAO,2BAA2B,UAAU,oCAAoC,UAAU,OAAO,OAAO;AACzG;AAHgB;AAKT,SAAS,gCAAsD,OAAe,SAA4C;AAChI,QAAM,WAAW,sBAAsB,KAAK;AAC5C,SAAO,2BAA2B,iBAAiB,2CAA2C,UAAU,OAAO,OAAO;AACvH;AAHgB;AAKT,SAAS,4BAAkD,OAAe,SAA4C;AAC5H,QAAM,WAAW,qBAAqB,KAAK;AAC3C,SAAO,2BAA2B,aAAa,uCAAuC,UAAU,OAAO,OAAO;AAC/G;AAHgB;AAKT,SAAS,mCAAyD,OAAe,SAA4C;AACnI,QAAM,WAAW,sBAAsB,KAAK;AAC5C,SAAO,2BAA2B,oBAAoB,8CAA8C,UAAU,OAAO,OAAO;AAC7H;AAHgB;AAKT,SAAS,sBAA4C,OAAe,SAA4C;AACtH,QAAM,WAAW,uBAAuB,KAAK;AAC7C,SAAO,2BAA2B,OAAO,iCAAiC,UAAU,OAAO,OAAO;AACnG;AAHgB;AAKT,SAAS,yBAA+C,OAAe,SAA4C;AACzH,QAAM,WAAW,uBAAuB,KAAK;AAC7C,SAAO,2BAA2B,UAAU,oCAAoC,UAAU,OAAO,OAAO;AACzG;AAHgB;AAKT,SAAS,sBAA4C,OAAe,WAAmB,SAA4C;AACzI,QAAM,WAAW,sBAAsB,KAAK,yBAAyB,SAAS;AAC9E,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,UAAU,SAAS,MAAM,SAAS,YAC5C,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AAhBgB;AAkBT,SAAS,+BAAqD,OAAe,KAAa,SAA4C;AAC5I,QAAM,WAAW,sBAAsB,KAAK,0BAA0B,GAAG;AACzE,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,UAAU,SAAS,MAAM,UAAU,MAC7C,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AAhBgB;AAkBT,SAAS,+BACf,YACA,WACA,SACiB;AACjB,QAAM,WAAW,qBAAqB,UAAU,yBAAyB,SAAS;AAClF,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,SAAS,cAAc,MAAM,SAAS,YAChD,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA,SAAS,WAAW;AAAA,UACpB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACH;AAAA,EACD;AACD;AApBgB;;;AC5MhB,IAAM,SAAS,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAEhC,IAAM,QAAQ,wBAAC,SAAiB;AACtC,SAAO,GAAG,OAAO,SAAS,KAAK,CAAC,EAAE,YAAY,CAAC,IAAI,OAAO,GAAG,IAAI,IAAI;AACtE,GAFqB;;;ACWd,IAAM,cAAc;AAAA,EAC1B,WAAW,CAACyC,OAA+BA,cAAa;AAAA,EACxD,YAAY,CAACA,OAAgCA,cAAa;AAAA,EAC1D,mBAAmB,CAACA,OAAuCA,cAAa;AAAA,EACxE,YAAY,CAACA,OAAgCA,cAAa;AAAA,EAC1D,aAAa,CAACA,OAAiCA,cAAa;AAAA,EAC5D,YAAY,CAACA,OAAgCA,cAAa;AAAA,EAC1D,aAAa,CAACA,OAAiCA,cAAa;AAAA,EAC5D,cAAc,CAACA,OAAkCA,cAAa;AAAA,EAC9D,cAAc,CAACA,OAAkCA,cAAa;AAAA,EAC9D,eAAe,CAACA,OAAmCA,cAAa;AAAA,EAChE,gBAAgB,CAACA,OAAoCA,cAAa;AAAA,EAClE,YAAY,CAACA,OAAgC,YAAY,OAAOA,EAAC,KAAK,EAAEA,cAAa;AACtF;;;ACEO,IAAM,uBAAN,MAAM,6BAAkD,cAAiB;AAAA,EAGxE,YAAY,MAAsB,mBAAqC,CAAC,GAAG,cAAyC,CAAC,GAAG;AAC9H,UAAM,kBAAkB,WAAW;AACnC,SAAK,OAAO;AAAA,EACb;AAAA,EAEO,mBAAmB,QAAgB,UAA4B,KAAK,kBAAkB;AAC5F,WAAO,KAAK,cAAc,6BAA6B,QAAQ,OAAO,CAAC;AAAA,EACxE;AAAA,EAEO,0BAA0B,QAAgB,UAA4B,KAAK,kBAAkB;AACnG,WAAO,KAAK,cAAc,oCAAoC,QAAQ,OAAO,CAAC;AAAA,EAC/E;AAAA,EAEO,sBAAsB,QAAgB,UAA4B,KAAK,kBAAkB;AAC/F,WAAO,KAAK,cAAc,gCAAgC,QAAQ,OAAO,CAAC;AAAA,EAC3E;AAAA,EAEO,6BAA6B,QAAgB,UAA4B,KAAK,kBAAkB;AACtG,WAAO,KAAK,cAAc,uCAAuC,QAAQ,OAAO,CAAC;AAAA,EAClF;AAAA,EAEO,gBAAgB,QAAgB,UAA4B,KAAK,kBAAkB;AACzF,WAAO,KAAK,cAAc,0BAA0B,QAAQ,OAAO,CAAC;AAAA,EACrE;AAAA,EAEO,mBAAmB,QAAgB,UAA4B,KAAK,kBAAkB;AAC5F,WAAO,KAAK,cAAc,6BAA6B,QAAQ,OAAO,CAAC;AAAA,EACxE;AAAA,EAEO,gBAAgB,OAAe,WAAmB,UAA4B,KAAK,kBAAkB;AAC3G,WAAO,KAAK,cAAc,0BAA0B,OAAO,WAAW,OAAO,CAAC;AAAA,EAC/E;AAAA,EAEO,yBAAyB,SAAiB,OAAe,UAA4B,KAAK,kBAAkB;AAClH,WAAO,KAAK,cAAc,mCAAmC,SAAS,OAAO,OAAO,CAAmB;AAAA,EACxG;AAAA,EAEO,yBAAyB,YAAoB,WAAmB,UAA4B,KAAK,kBAAkB;AACzH,WAAO,KAAK,cAAc,mCAAmC,YAAY,WAAW,OAAO,CAAC;AAAA,EAC7F;AAAA,EAEO,eAAe,QAAgB,UAA4B,KAAK,kBAAkB;AACxF,WAAO,KAAK,cAAc,yBAAyB,QAAQ,OAAO,CAAC;AAAA,EACpE;AAAA,EAEO,sBAAsB,QAAgB,UAA4B,KAAK,kBAAkB;AAC/F,WAAO,KAAK,cAAc,gCAAgC,QAAQ,OAAO,CAAC;AAAA,EAC3E;AAAA,EAEO,kBAAkB,QAAgB,UAA4B,KAAK,kBAAkB;AAC3F,WAAO,KAAK,cAAc,4BAA4B,QAAQ,OAAO,CAAC;AAAA,EACvE;AAAA,EAEO,yBAAyB,QAAgB,UAA4B,KAAK,kBAAkB;AAClG,WAAO,KAAK,cAAc,mCAAmC,QAAQ,OAAO,CAAC;AAAA,EAC9E;AAAA,EAEO,YAAY,QAAgB,UAA4B,KAAK,kBAAkB;AACrF,WAAO,KAAK,cAAc,sBAAsB,QAAQ,OAAO,CAAC;AAAA,EACjE;AAAA,EAEO,eAAe,QAAgB,UAA4B,KAAK,kBAAkB;AACxF,WAAO,KAAK,cAAc,yBAAyB,QAAQ,OAAO,CAAC;AAAA,EACpE;AAAA,EAEO,YAAY,OAAe,WAAmB,UAA4B,KAAK,kBAAkB;AACvG,WAAO,KAAK,cAAc,sBAAsB,OAAO,WAAW,OAAO,CAAC;AAAA,EAC3E;AAAA,EAEO,qBAAqB,SAAiB,OAAe,UAA4B,KAAK,kBAAkB;AAC9G,WAAO,KAAK,cAAc,+BAA+B,SAAS,OAAO,OAAO,CAAC;AAAA,EAClF;AAAA,EAEO,qBAAqB,YAAoB,WAAmB,UAA4B,KAAK,kBAAkB;AACrH,WAAO,KAAK,cAAc,+BAA+B,YAAY,WAAW,OAAO,CAAC;AAAA,EACzF;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,MAAM,KAAK,kBAAkB,KAAK,WAAW,CAAC;AAAA,EAChG;AAAA,EAEU,OAAO,OAA4C;AAC5D,WAAO,YAAY,KAAK,IAAI,EAAE,KAAK,IAChC,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,gBAAgB,kBAAkB,KAAK,iBAAiB,WAAW,YAAY,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC;AAAA,EAC5H;AACD;AAzFgF;AAAzE,IAAM,sBAAN;;;ACAA,IAAM,UAAN,MAAM,QAAO;AAAA,EACZ,OAAO,SAA4B;AACzC,WAAO,IAAI,gBAAgB,OAAO;AAAA,EACnC;AAAA,EAEO,OAAO,SAA4B;AACzC,WAAO,IAAI,gBAAgB,OAAO;AAAA,EACnC;AAAA,EAEO,OAAO,SAA4B;AACzC,WAAO,IAAI,gBAAgB,OAAO;AAAA,EACnC;AAAA,EAEO,QAAQ,SAA4B;AAC1C,WAAO,IAAI,iBAAiB,OAAO;AAAA,EACpC;AAAA,EAEO,KAAK,SAA4B;AACvC,WAAO,IAAI,cAAc,OAAO;AAAA,EACjC;AAAA,EAEO,OAAyB,OAAiC,SAA4B;AAC5F,WAAO,IAAI,gBAAmB,uBAAuC,OAAO;AAAA,EAC7E;AAAA,EAEO,UAAU,SAA4B;AAC5C,WAAO,KAAK,QAAQ,QAAW,EAAE,eAAe,QAAQ,CAAC;AAAA,EAC1D;AAAA,EAEO,KAAK,SAA4B;AACvC,WAAO,KAAK,QAAQ,MAAM,EAAE,eAAe,QAAQ,CAAC;AAAA,EACrD;AAAA,EAEO,QAAQ,SAA4B;AAC1C,WAAO,IAAI,iBAAiB,OAAO;AAAA,EACpC;AAAA,EAEO,IAAI,SAA4B;AACtC,WAAO,IAAI,qBAA0B,OAAO;AAAA,EAC7C;AAAA,EAEO,QAAQ,SAA4B;AAC1C,WAAO,IAAI,qBAA8B,OAAO;AAAA,EACjD;AAAA,EAEO,MAAM,SAA4B;AACxC,WAAO,IAAI,eAAe,OAAO;AAAA,EAClC;AAAA,EAEO,KAAQ,QAAsB,SAA4B;AAChE,WAAO,KAAK;AAAA,MACX,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,OAAO,EAAE,eAAe,QAAQ,CAAC,CAAC;AAAA,MACrE;AAAA,IACD;AAAA,EACD;AAAA,EAEO,WAAqC,WAAc,SAAoD;AAC7G,WAAO,IAAI,oBAAoB,WAAW,OAAO;AAAA,EAClD;AAAA,EAEO,QAAW,OAAU,SAAkG;AAC7H,QAAI,iBAAiB,MAAM;AAC1B,aAAO,KAAK,KAAK,SAAS,WAAW,EAAE,MAAM,OAAO,SAAS,aAAa;AAAA,IAC3E;AAEA,WAAO,IAAI,iBAAiB,OAAO,SAAS,aAAa;AAAA,EAC1D;AAAA,EAEO,SAAY,UAA0B,SAAkD;AAC9F,WAAO,IAAI,kBAAkB,UAAU,OAAO;AAAA,EAC/C;AAAA,EAEO,MAAsC,YAAe,SAA+D;AAC1H,WAAO,IAAI,eAAe,YAAY,OAAO;AAAA,EAC9C;AAAA,EAIO,MAA2B,WAAqC,SAA4B;AAClG,WAAO,IAAI,eAAe,WAAW,OAAO;AAAA,EAC7C;AAAA,EAEO,WAAiC,OAAuB,cAAc,SAA4B;AACxG,WAAO,IAAI,oBAAuB,MAAM,OAAO;AAAA,EAChD;AAAA,EAEO,UAAU,SAA4B;AAC5C,WAAO,KAAK,WAAsB,aAAa,OAAO;AAAA,EACvD;AAAA,EAEO,WAAW,SAA4B;AAC7C,WAAO,KAAK,WAAuB,cAAc,OAAO;AAAA,EACzD;AAAA,EAEO,kBAAkB,SAA4B;AACpD,WAAO,KAAK,WAA8B,qBAAqB,OAAO;AAAA,EACvE;AAAA,EAEO,WAAW,SAA4B;AAC7C,WAAO,KAAK,WAAuB,cAAc,OAAO;AAAA,EACzD;AAAA,EAEO,YAAY,SAA4B;AAC9C,WAAO,KAAK,WAAwB,eAAe,OAAO;AAAA,EAC3D;AAAA,EAEO,WAAW,SAA4B;AAC7C,WAAO,KAAK,WAAuB,cAAc,OAAO;AAAA,EACzD;AAAA,EAEO,YAAY,SAA4B;AAC9C,WAAO,KAAK,WAAwB,eAAe,OAAO;AAAA,EAC3D;AAAA,EAEO,aAAa,SAA4B;AAC/C,WAAO,KAAK,WAAyB,gBAAgB,OAAO;AAAA,EAC7D;AAAA,EAEO,aAAa,SAA4B;AAC/C,WAAO,KAAK,WAAyB,gBAAgB,OAAO;AAAA,EAC7D;AAAA,EAEO,cAAc,SAA4B;AAChD,WAAO,KAAK,WAA0B,iBAAiB,OAAO;AAAA,EAC/D;AAAA,EAEO,eAAe,SAA4B;AACjD,WAAO,KAAK,WAA2B,kBAAkB,OAAO;AAAA,EACjE;AAAA,EAEO,MAA2C,YAAoB,SAA4D;AACjI,WAAO,IAAI,eAAe,YAAY,OAAO;AAAA,EAC9C;AAAA,EAEO,IAAO,WAA6B,SAA4B;AACtE,WAAO,IAAI,aAAa,WAAW,OAAO;AAAA,EAC3C;AAAA,EAEO,OAAU,WAA6B,SAA4B;AACzE,WAAO,IAAI,gBAAgB,WAAW,OAAO;AAAA,EAC9C;AAAA,EAEO,IAAU,cAAgC,gBAAkC,SAA4B;AAC9G,WAAO,IAAI,aAAa,cAAc,gBAAgB,OAAO;AAAA,EAC9D;AAAA,EAEO,KAAuC,WAAkC,SAA4B;AAC3G,WAAO,IAAI,cAAc,WAAW,OAAO;AAAA,EAC5C;AACD;AArJoB;AAAb,IAAM,SAAN;;;AC1BA,IAAMtC,KAAI,IAAI,OAAO", "sourcesContent": ["import get from 'lodash/get.js';\nimport { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport type { BaseValidator } from '../validators/BaseValidator';\nimport type { IConstraint } from './base/IConstraint';\n\nexport type ObjectConstraintName = `s.object(T.when)`;\n\nexport type WhenKey = PropertyKey | PropertyKey[];\n\nexport interface WhenOptions<T extends BaseValidator<any>, Key extends WhenKey> {\n\tis?: boolean | ((value: Key extends Array<any> ? any[] : any) => boolean);\n\tthen: (predicate: T) => T;\n\totherwise?: (predicate: T) => T;\n}\n\nexport function whenConstraint<T extends BaseValidator<any>, I, Key extends WhenKey>(\n\tkey: Key,\n\toptions: WhenOptions<T, Key>,\n\tvalidator: T,\n\tvalidatorOptions?: ValidatorOptions\n): IConstraint<I> {\n\treturn {\n\t\trun(input: I, parent?: any) {\n\t\t\tif (!parent) {\n\t\t\t\treturn Result.err(\n\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t's.object(T.when)',\n\t\t\t\t\t\tvalidatorOptions?.message ?? 'Validator has no parent',\n\t\t\t\t\t\tparent,\n\t\t\t\t\t\t'Validator to have a parent'\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tconst isKeyArray = Array.isArray(key);\n\n\t\t\tconst value = isKeyArray ? key.map((k) => get(parent, k)) : get(parent, key);\n\n\t\t\tconst predicate = resolveBooleanIs<T, Key>(options, value, isKeyArray) ? options.then : options.otherwise;\n\n\t\t\tif (predicate) {\n\t\t\t\treturn predicate(validator).run(input) as Result<I, ExpectedConstraintError<I>>;\n\t\t\t}\n\n\t\t\treturn Result.ok(input);\n\t\t}\n\t};\n}\n\nfunction resolveBooleanIs<T extends BaseValidator<any>, Key extends WhenKey>(options: WhenOptions<T, Key>, value: any, isKeyArray: boolean) {\n\tif (options.is === undefined) {\n\t\treturn isKeyArray ? !value.some((val: any) => !val) : Boolean(value);\n\t}\n\n\tif (typeof options.is === 'function') {\n\t\treturn options.is(value);\n\t}\n\n\treturn value === options.is;\n}\n", "var e,t,n,r=\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof self?self:global,o=e={};function i(){throw new Error(\"setTimeout has not been defined\")}function u(){throw new Error(\"clearTimeout has not been defined\")}function c(e){if(t===setTimeout)return setTimeout(e,0);if((t===i||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this||r,e,0)}}}!function(){try{t=\"function\"==typeof setTimeout?setTimeout:i;}catch(e){t=i;}try{n=\"function\"==typeof clearTimeout?clearTimeout:u;}catch(e){n=u;}}();var l,s=[],f=!1,a=-1;function h(){f&&l&&(f=!1,l.length?s=l.concat(s):a=-1,s.length&&d());}function d(){if(!f){var e=c(h);f=!0;for(var t=s.length;t;){for(l=s,s=[];++a<t;)l&&l[a].run();a=-1,t=s.length;}l=null,f=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===u||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e);}catch(t){try{return n.call(null,e)}catch(t){return n.call(this||r,e)}}}(e);}}function m(e,t){(this||r).fun=e,(this||r).array=t;}function p(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];s.push(new m(e,t)),1!==s.length||f||c(d);},m.prototype.run=function(){(this||r).fun.apply(null,(this||r).array);},o.title=\"browser\",o.browser=!0,o.env={},o.argv=[],o.version=\"\",o.versions={},o.on=p,o.addListener=p,o.once=p,o.off=p,o.removeListener=p,o.removeAllListeners=p,o.emit=p,o.prependListener=p,o.prependOnceListener=p,o.listeners=function(e){return []},o.binding=function(e){throw new Error(\"process.binding is not supported\")},o.cwd=function(){return \"/\"},o.chdir=function(e){throw new Error(\"process.chdir is not supported\")},o.umask=function(){return 0};var T=e;T.addListener;T.argv;T.binding;T.browser;T.chdir;T.cwd;T.emit;T.env;T.listeners;T.nextTick;T.off;T.on;T.once;T.prependListener;T.prependOnceListener;T.removeAllListeners;T.removeListener;T.title;T.umask;T.version;T.versions;\n\nexport { T };\n", "import { T as T$1 } from './chunk-5decc758.js';\n\nvar t=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.toStringTag,e=Object.prototype.toString,o=function(o){return !(t&&o&&\"object\"==typeof o&&Symbol.toStringTag in o)&&\"[object Arguments]\"===e.call(o)},n=function(t){return !!o(t)||null!==t&&\"object\"==typeof t&&\"number\"==typeof t.length&&t.length>=0&&\"[object Array]\"!==e.call(t)&&\"[object Function]\"===e.call(t.callee)},r=function(){return o(arguments)}();o.isLegacyArguments=n;var l=r?o:n;var t$1=Object.prototype.toString,o$1=Function.prototype.toString,n$1=/^\\s*(?:function)?\\*/,e$1=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.toStringTag,r$1=Object.getPrototypeOf,c=function(){if(!e$1)return !1;try{return Function(\"return function*() {}\")()}catch(t){}}(),u=c?r$1(c):{},i=function(c){return \"function\"==typeof c&&(!!n$1.test(o$1.call(c))||(e$1?r$1(c)===u:\"[object GeneratorFunction]\"===t$1.call(c)))};var t$2=\"function\"==typeof Object.create?function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}));}:function(t,e){if(e){t.super_=e;var o=function(){};o.prototype=e.prototype,t.prototype=new o,t.prototype.constructor=t;}};var i$1=function(e){return e&&\"object\"==typeof e&&\"function\"==typeof e.copy&&\"function\"==typeof e.fill&&\"function\"==typeof e.readUInt8},o$2={},u$1=i$1,f=l,a=i;function c$1(e){return e.call.bind(e)}var s=\"undefined\"!=typeof BigInt,p=\"undefined\"!=typeof Symbol,y=p&&void 0!==Symbol.toStringTag,l$1=\"undefined\"!=typeof Uint8Array,d=\"undefined\"!=typeof ArrayBuffer;if(l$1&&y)var g=Object.getPrototypeOf(Uint8Array.prototype),b=c$1(Object.getOwnPropertyDescriptor(g,Symbol.toStringTag).get);var m=c$1(Object.prototype.toString),h=c$1(Number.prototype.valueOf),j=c$1(String.prototype.valueOf),A=c$1(Boolean.prototype.valueOf);if(s)var w=c$1(BigInt.prototype.valueOf);if(p)var v=c$1(Symbol.prototype.valueOf);function O(e,t){if(\"object\"!=typeof e)return !1;try{return t(e),!0}catch(e){return !1}}function S(e){return l$1&&y?void 0!==b(e):B(e)||k(e)||E(e)||D(e)||U(e)||P(e)||x(e)||I(e)||M(e)||z(e)||F(e)}function B(e){return l$1&&y?\"Uint8Array\"===b(e):\"[object Uint8Array]\"===m(e)||u$1(e)&&void 0!==e.buffer}function k(e){return l$1&&y?\"Uint8ClampedArray\"===b(e):\"[object Uint8ClampedArray]\"===m(e)}function E(e){return l$1&&y?\"Uint16Array\"===b(e):\"[object Uint16Array]\"===m(e)}function D(e){return l$1&&y?\"Uint32Array\"===b(e):\"[object Uint32Array]\"===m(e)}function U(e){return l$1&&y?\"Int8Array\"===b(e):\"[object Int8Array]\"===m(e)}function P(e){return l$1&&y?\"Int16Array\"===b(e):\"[object Int16Array]\"===m(e)}function x(e){return l$1&&y?\"Int32Array\"===b(e):\"[object Int32Array]\"===m(e)}function I(e){return l$1&&y?\"Float32Array\"===b(e):\"[object Float32Array]\"===m(e)}function M(e){return l$1&&y?\"Float64Array\"===b(e):\"[object Float64Array]\"===m(e)}function z(e){return l$1&&y?\"BigInt64Array\"===b(e):\"[object BigInt64Array]\"===m(e)}function F(e){return l$1&&y?\"BigUint64Array\"===b(e):\"[object BigUint64Array]\"===m(e)}function T(e){return \"[object Map]\"===m(e)}function N(e){return \"[object Set]\"===m(e)}function W(e){return \"[object WeakMap]\"===m(e)}function $(e){return \"[object WeakSet]\"===m(e)}function C(e){return \"[object ArrayBuffer]\"===m(e)}function V(e){return \"undefined\"!=typeof ArrayBuffer&&(C.working?C(e):e instanceof ArrayBuffer)}function G(e){return \"[object DataView]\"===m(e)}function R(e){return \"undefined\"!=typeof DataView&&(G.working?G(e):e instanceof DataView)}function J(e){return \"[object SharedArrayBuffer]\"===m(e)}function _(e){return \"undefined\"!=typeof SharedArrayBuffer&&(J.working?J(e):e instanceof SharedArrayBuffer)}function H(e){return O(e,h)}function Z(e){return O(e,j)}function q(e){return O(e,A)}function K(e){return s&&O(e,w)}function L(e){return p&&O(e,v)}o$2.isArgumentsObject=f,o$2.isGeneratorFunction=a,o$2.isPromise=function(e){return \"undefined\"!=typeof Promise&&e instanceof Promise||null!==e&&\"object\"==typeof e&&\"function\"==typeof e.then&&\"function\"==typeof e.catch},o$2.isArrayBufferView=function(e){return d&&ArrayBuffer.isView?ArrayBuffer.isView(e):S(e)||R(e)},o$2.isTypedArray=S,o$2.isUint8Array=B,o$2.isUint8ClampedArray=k,o$2.isUint16Array=E,o$2.isUint32Array=D,o$2.isInt8Array=U,o$2.isInt16Array=P,o$2.isInt32Array=x,o$2.isFloat32Array=I,o$2.isFloat64Array=M,o$2.isBigInt64Array=z,o$2.isBigUint64Array=F,T.working=\"undefined\"!=typeof Map&&T(new Map),o$2.isMap=function(e){return \"undefined\"!=typeof Map&&(T.working?T(e):e instanceof Map)},N.working=\"undefined\"!=typeof Set&&N(new Set),o$2.isSet=function(e){return \"undefined\"!=typeof Set&&(N.working?N(e):e instanceof Set)},W.working=\"undefined\"!=typeof WeakMap&&W(new WeakMap),o$2.isWeakMap=function(e){return \"undefined\"!=typeof WeakMap&&(W.working?W(e):e instanceof WeakMap)},$.working=\"undefined\"!=typeof WeakSet&&$(new WeakSet),o$2.isWeakSet=function(e){return $(e)},C.working=\"undefined\"!=typeof ArrayBuffer&&C(new ArrayBuffer),o$2.isArrayBuffer=V,G.working=\"undefined\"!=typeof ArrayBuffer&&\"undefined\"!=typeof DataView&&G(new DataView(new ArrayBuffer(1),0,1)),o$2.isDataView=R,J.working=\"undefined\"!=typeof SharedArrayBuffer&&J(new SharedArrayBuffer),o$2.isSharedArrayBuffer=_,o$2.isAsyncFunction=function(e){return \"[object AsyncFunction]\"===m(e)},o$2.isMapIterator=function(e){return \"[object Map Iterator]\"===m(e)},o$2.isSetIterator=function(e){return \"[object Set Iterator]\"===m(e)},o$2.isGeneratorObject=function(e){return \"[object Generator]\"===m(e)},o$2.isWebAssemblyCompiledModule=function(e){return \"[object WebAssembly.Module]\"===m(e)},o$2.isNumberObject=H,o$2.isStringObject=Z,o$2.isBooleanObject=q,o$2.isBigIntObject=K,o$2.isSymbolObject=L,o$2.isBoxedPrimitive=function(e){return H(e)||Z(e)||q(e)||K(e)||L(e)},o$2.isAnyArrayBuffer=function(e){return l$1&&(V(e)||_(e))},[\"isProxy\",\"isExternal\",\"isModuleNamespaceObject\"].forEach((function(e){Object.defineProperty(o$2,e,{enumerable:!1,value:function(){throw new Error(e+\" is not supported in userland\")}});}));var Q=\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof self?self:global,X={},Y=T$1,ee=Object.getOwnPropertyDescriptors||function(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++)r[t[n]]=Object.getOwnPropertyDescriptor(e,t[n]);return r},te=/%[sdj%]/g;X.format=function(e){if(!ge(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(oe(arguments[r]));return t.join(\" \")}r=1;for(var n=arguments,i=n.length,o=String(e).replace(te,(function(e){if(\"%%\"===e)return \"%\";if(r>=i)return e;switch(e){case\"%s\":return String(n[r++]);case\"%d\":return Number(n[r++]);case\"%j\":try{return JSON.stringify(n[r++])}catch(e){return \"[Circular]\"}default:return e}})),u=n[r];r<i;u=n[++r])le(u)||!he(u)?o+=\" \"+u:o+=\" \"+oe(u);return o},X.deprecate=function(e,t){if(void 0!==Y&&!0===Y.noDeprecation)return e;if(void 0===Y)return function(){return X.deprecate(e,t).apply(this||Q,arguments)};var r=!1;return function(){if(!r){if(Y.throwDeprecation)throw new Error(t);Y.traceDeprecation?console.trace(t):console.error(t),r=!0;}return e.apply(this||Q,arguments)}};var re={},ne=/^$/;if(Y.env.NODE_DEBUG){var ie=Y.env.NODE_DEBUG;ie=ie.replace(/[|\\\\{}()[\\]^$+?.]/g,\"\\\\$&\").replace(/\\*/g,\".*\").replace(/,/g,\"$|^\").toUpperCase(),ne=new RegExp(\"^\"+ie+\"$\",\"i\");}function oe(e,t){var r={seen:[],stylize:fe};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),ye(t)?r.showHidden=t:t&&X._extend(r,t),be(r.showHidden)&&(r.showHidden=!1),be(r.depth)&&(r.depth=2),be(r.colors)&&(r.colors=!1),be(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=ue),ae(r,e,r.depth)}function ue(e,t){var r=oe.styles[t];return r?\"\u001b[\"+oe.colors[r][0]+\"m\"+e+\"\u001b[\"+oe.colors[r][1]+\"m\":e}function fe(e,t){return e}function ae(e,t,r){if(e.customInspect&&t&&we(t.inspect)&&t.inspect!==X.inspect&&(!t.constructor||t.constructor.prototype!==t)){var n=t.inspect(r,e);return ge(n)||(n=ae(e,n,r)),n}var i=function(e,t){if(be(t))return e.stylize(\"undefined\",\"undefined\");if(ge(t)){var r=\"'\"+JSON.stringify(t).replace(/^\"|\"$/g,\"\").replace(/'/g,\"\\\\'\").replace(/\\\\\"/g,'\"')+\"'\";return e.stylize(r,\"string\")}if(de(t))return e.stylize(\"\"+t,\"number\");if(ye(t))return e.stylize(\"\"+t,\"boolean\");if(le(t))return e.stylize(\"null\",\"null\")}(e,t);if(i)return i;var o=Object.keys(t),u=function(e){var t={};return e.forEach((function(e,r){t[e]=!0;})),t}(o);if(e.showHidden&&(o=Object.getOwnPropertyNames(t)),Ae(t)&&(o.indexOf(\"message\")>=0||o.indexOf(\"description\")>=0))return ce(t);if(0===o.length){if(we(t)){var f=t.name?\": \"+t.name:\"\";return e.stylize(\"[Function\"+f+\"]\",\"special\")}if(me(t))return e.stylize(RegExp.prototype.toString.call(t),\"regexp\");if(je(t))return e.stylize(Date.prototype.toString.call(t),\"date\");if(Ae(t))return ce(t)}var a,c=\"\",s=!1,p=[\"{\",\"}\"];(pe(t)&&(s=!0,p=[\"[\",\"]\"]),we(t))&&(c=\" [Function\"+(t.name?\": \"+t.name:\"\")+\"]\");return me(t)&&(c=\" \"+RegExp.prototype.toString.call(t)),je(t)&&(c=\" \"+Date.prototype.toUTCString.call(t)),Ae(t)&&(c=\" \"+ce(t)),0!==o.length||s&&0!=t.length?r<0?me(t)?e.stylize(RegExp.prototype.toString.call(t),\"regexp\"):e.stylize(\"[Object]\",\"special\"):(e.seen.push(t),a=s?function(e,t,r,n,i){for(var o=[],u=0,f=t.length;u<f;++u)ke(t,String(u))?o.push(se(e,t,r,n,String(u),!0)):o.push(\"\");return i.forEach((function(i){i.match(/^\\d+$/)||o.push(se(e,t,r,n,i,!0));})),o}(e,t,r,u,o):o.map((function(n){return se(e,t,r,u,n,s)})),e.seen.pop(),function(e,t,r){var n=0;if(e.reduce((function(e,t){return n++,t.indexOf(\"\\n\")>=0&&n++,e+t.replace(/\\u001b\\[\\d\\d?m/g,\"\").length+1}),0)>60)return r[0]+(\"\"===t?\"\":t+\"\\n \")+\" \"+e.join(\",\\n  \")+\" \"+r[1];return r[0]+t+\" \"+e.join(\", \")+\" \"+r[1]}(a,c,p)):p[0]+c+p[1]}function ce(e){return \"[\"+Error.prototype.toString.call(e)+\"]\"}function se(e,t,r,n,i,o){var u,f,a;if((a=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]}).get?f=a.set?e.stylize(\"[Getter/Setter]\",\"special\"):e.stylize(\"[Getter]\",\"special\"):a.set&&(f=e.stylize(\"[Setter]\",\"special\")),ke(n,i)||(u=\"[\"+i+\"]\"),f||(e.seen.indexOf(a.value)<0?(f=le(r)?ae(e,a.value,null):ae(e,a.value,r-1)).indexOf(\"\\n\")>-1&&(f=o?f.split(\"\\n\").map((function(e){return \"  \"+e})).join(\"\\n\").substr(2):\"\\n\"+f.split(\"\\n\").map((function(e){return \"   \"+e})).join(\"\\n\")):f=e.stylize(\"[Circular]\",\"special\")),be(u)){if(o&&i.match(/^\\d+$/))return f;(u=JSON.stringify(\"\"+i)).match(/^\"([a-zA-Z_][a-zA-Z_0-9]*)\"$/)?(u=u.substr(1,u.length-2),u=e.stylize(u,\"name\")):(u=u.replace(/'/g,\"\\\\'\").replace(/\\\\\"/g,'\"').replace(/(^\"|\"$)/g,\"'\"),u=e.stylize(u,\"string\"));}return u+\": \"+f}function pe(e){return Array.isArray(e)}function ye(e){return \"boolean\"==typeof e}function le(e){return null===e}function de(e){return \"number\"==typeof e}function ge(e){return \"string\"==typeof e}function be(e){return void 0===e}function me(e){return he(e)&&\"[object RegExp]\"===ve(e)}function he(e){return \"object\"==typeof e&&null!==e}function je(e){return he(e)&&\"[object Date]\"===ve(e)}function Ae(e){return he(e)&&(\"[object Error]\"===ve(e)||e instanceof Error)}function we(e){return \"function\"==typeof e}function ve(e){return Object.prototype.toString.call(e)}function Oe(e){return e<10?\"0\"+e.toString(10):e.toString(10)}X.debuglog=function(e){if(e=e.toUpperCase(),!re[e])if(ne.test(e)){var t=Y.pid;re[e]=function(){var r=X.format.apply(X,arguments);console.error(\"%s %d: %s\",e,t,r);};}else re[e]=function(){};return re[e]},X.inspect=oe,oe.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},oe.styles={special:\"cyan\",number:\"yellow\",boolean:\"yellow\",undefined:\"grey\",null:\"bold\",string:\"green\",date:\"magenta\",regexp:\"red\"},X.types=o$2,X.isArray=pe,X.isBoolean=ye,X.isNull=le,X.isNullOrUndefined=function(e){return null==e},X.isNumber=de,X.isString=ge,X.isSymbol=function(e){return \"symbol\"==typeof e},X.isUndefined=be,X.isRegExp=me,X.types.isRegExp=me,X.isObject=he,X.isDate=je,X.types.isDate=je,X.isError=Ae,X.types.isNativeError=Ae,X.isFunction=we,X.isPrimitive=function(e){return null===e||\"boolean\"==typeof e||\"number\"==typeof e||\"string\"==typeof e||\"symbol\"==typeof e||void 0===e},X.isBuffer=i$1;var Se=[\"Jan\",\"Feb\",\"Mar\",\"Apr\",\"May\",\"Jun\",\"Jul\",\"Aug\",\"Sep\",\"Oct\",\"Nov\",\"Dec\"];function Be(){var e=new Date,t=[Oe(e.getHours()),Oe(e.getMinutes()),Oe(e.getSeconds())].join(\":\");return [e.getDate(),Se[e.getMonth()],t].join(\" \")}function ke(e,t){return Object.prototype.hasOwnProperty.call(e,t)}X.log=function(){console.log(\"%s - %s\",Be(),X.format.apply(X,arguments));},X.inherits=t$2,X._extend=function(e,t){if(!t||!he(t))return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e};var Ee=\"undefined\"!=typeof Symbol?Symbol(\"util.promisify.custom\"):void 0;function De(e,t){if(!e){var r=new Error(\"Promise was rejected with a falsy value\");r.reason=e,e=r;}return t(e)}X.promisify=function(e){if(\"function\"!=typeof e)throw new TypeError('The \"original\" argument must be of type Function');if(Ee&&e[Ee]){var t;if(\"function\"!=typeof(t=e[Ee]))throw new TypeError('The \"util.promisify.custom\" argument must be of type Function');return Object.defineProperty(t,Ee,{value:t,enumerable:!1,writable:!1,configurable:!0}),t}function t(){for(var t,r,n=new Promise((function(e,n){t=e,r=n;})),i=[],o=0;o<arguments.length;o++)i.push(arguments[o]);i.push((function(e,n){e?r(e):t(n);}));try{e.apply(this||Q,i);}catch(e){r(e);}return n}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),Ee&&Object.defineProperty(t,Ee,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,ee(e))},X.promisify.custom=Ee,X.callbackify=function(e){if(\"function\"!=typeof e)throw new TypeError('The \"original\" argument must be of type Function');function t(){for(var t=[],r=0;r<arguments.length;r++)t.push(arguments[r]);var n=t.pop();if(\"function\"!=typeof n)throw new TypeError(\"The last argument must be of type Function\");var i=this||Q,o=function(){return n.apply(i,arguments)};e.apply(this||Q,t).then((function(e){Y.nextTick(o.bind(null,null,e));}),(function(e){Y.nextTick(De.bind(null,e,o));}));}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),Object.defineProperties(t,ee(e)),t};\n\nexport { X, t$2 as t };\n", "import { X } from './chunk-b4205b57.js';\nimport './chunk-5decc758.js';\n\nX._extend;X.callbackify;X.debuglog;X.deprecate;X.format;X.inherits;X.inspect;X.isArray;X.isBoolean;X.isBuffer;X.isDate;X.isError;X.isFunction;X.isNull;X.isNullOrUndefined;X.isNumber;X.isObject;X.isPrimitive;X.isRegExp;X.isString;X.isSymbol;X.isUndefined;X.log;X.promisify;\n\nvar _extend = X._extend;\nvar callbackify = X.callbackify;\nvar debuglog = X.debuglog;\nvar deprecate = X.deprecate;\nvar format = X.format;\nvar inherits = X.inherits;\nvar inspect = X.inspect;\nvar isArray = X.isArray;\nvar isBoolean = X.isBoolean;\nvar isBuffer = X.isBuffer;\nvar isDate = X.isDate;\nvar isError = X.isError;\nvar isFunction = X.isFunction;\nvar isNull = X.isNull;\nvar isNullOrUndefined = X.isNullOrUndefined;\nvar isNumber = X.isNumber;\nvar isObject = X.isObject;\nvar isPrimitive = X.isPrimitive;\nvar isRegExp = X.isRegExp;\nvar isString = X.isString;\nvar isSymbol = X.isSymbol;\nvar isUndefined = X.isUndefined;\nvar log = X.log;\nvar promisify = X.promisify;\nvar types = X.types;\n\nconst TextEncoder = self.TextEncoder;\nconst TextDecoder = self.TextDecoder;\n\nexport { TextDecoder as T, _extend as _, TextEncoder as a, deprecate as b, callbackify as c, debuglog as d, inspect as e, format as f, isArray as g, isBoolean as h, inherits as i, isBuffer as j, isDate as k, isError as l, isFunction as m, isNull as n, isNullOrUndefined as o, promisify as p, isNumber as q, isObject as r, isPrimitive as s, isRegExp as t, isString as u, isSymbol as v, isUndefined as w, log as x, types as y };\n", "import './chunk-ce0fbc82.js';\nimport { X } from './chunk-b4205b57.js';\nexport { X as default } from './chunk-b4205b57.js';\nimport './chunk-5decc758.js';\n\nvar _extend = X._extend;\r\nvar callbackify = X.callbackify;\r\nvar debuglog = X.debuglog;\r\nvar deprecate = X.deprecate;\r\nvar format = X.format;\r\nvar inherits = X.inherits;\r\nvar inspect = X.inspect;\r\nvar isArray = X.isArray;\r\nvar isBoolean = X.isBoolean;\r\nvar isBuffer = X.isBuffer;\r\nvar isDate = X.isDate;\r\nvar isError = X.isError;\r\nvar isFunction = X.isFunction;\r\nvar isNull = X.isNull;\r\nvar isNullOrUndefined = X.isNullOrUndefined;\r\nvar isNumber = X.isNumber;\r\nvar isObject = X.isObject;\r\nvar isPrimitive = X.isPrimitive;\r\nvar isRegExp = X.isRegExp;\r\nvar isString = X.isString;\r\nvar isSymbol = X.isSymbol;\r\nvar isUndefined = X.isUndefined;\r\nvar log = X.log;\r\nvar promisify = X.promisify;\r\nvar types = X.types;\r\n\r\nconst TextEncoder = X.TextEncoder = globalThis.TextEncoder;\r\nconst TextDecoder = X.TextDecoder = globalThis.TextDecoder;\n\nexport { TextDecoder, TextEncoder, _extend, callbackify, debuglog, deprecate, format, inherits, inspect, isArray, isBoolean, isBuffer, isDate, isError, isFunction, isNull, isNullOrUndefined, isNumber, isObject, isPrimitive, isRegExp, isString, isSymbol, isUndefined, log, promisify, types };\n", "import type { InspectOptionsStylized } from 'util';\nimport type { BaseErrorJsonified } from './error-types';\n\nexport const customInspectSymbol = Symbol.for('nodejs.util.inspect.custom');\nexport const customInspectSymbolStackLess = Symbol.for('nodejs.util.inspect.custom.stack-less');\n\nexport abstract class BaseError extends Error {\n\tpublic toJSON(): BaseErrorJsonified {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tmessage: this.message\n\t\t};\n\t}\n\n\tprotected [customInspectSymbol](depth: number, options: InspectOptionsStylized) {\n\t\treturn `${this[customInspectSymbolStackLess](depth, options)}\\n${this.stack!.slice(this.stack!.indexOf('\\n'))}`;\n\t}\n\n\tprotected abstract [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string;\n}\n", "import type {\n\tArrayConstraintName,\n\tBigIntConstraintName,\n\tBooleanConstraintName,\n\tDateConstraintName,\n\tNumberConstraintName,\n\tObjectConstraintName,\n\tStringConstraintName,\n\tTypedArrayConstraintName\n} from '../../constraints/type-exports';\nimport { BaseError } from './BaseError';\nimport type { BaseConstraintErrorJsonified } from './error-types';\n\nexport type ConstraintErrorNames =\n\t| TypedArrayConstraintName\n\t| ArrayConstraintName\n\t| BigIntConstraintName\n\t| BooleanConstraintName\n\t| DateConstraintName\n\t| NumberConstraintName\n\t| ObjectConstraintName\n\t| StringConstraintName;\n\nexport abstract class BaseConstraintError<T = unknown> extends BaseError {\n\tpublic readonly constraint: ConstraintErrorNames;\n\tpublic readonly given: T;\n\n\tpublic constructor(constraint: ConstraintErrorNames, message: string, given: T) {\n\t\tsuper(message);\n\t\tthis.constraint = constraint;\n\t\tthis.given = given;\n\t}\n\n\tpublic override toJSON(): BaseConstraintErrorJsonified<T> {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tconstraint: this.constraint,\n\t\t\tgiven: this.given,\n\t\t\tmessage: this.message\n\t\t};\n\t}\n}\n", "import { inspect, type InspectOptionsStylized } from 'util';\nimport { BaseConstraintError, type ConstraintErrorNames } from './BaseConstraintError';\nimport { customInspectSymbolStackLess } from './BaseError';\nimport type { ExpectedConstraintErrorJsonified } from './error-types';\n\nexport class ExpectedConstraintError<T = unknown> extends BaseConstraintError<T> {\n\tpublic readonly expected: string;\n\n\tpublic constructor(constraint: ConstraintErrorNames, message: string, given: T, expected: string) {\n\t\tsuper(constraint, message, given);\n\t\tthis.expected = expected;\n\t}\n\n\tpublic override toJSON(): ExpectedConstraintErrorJsonified<T> {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tconstraint: this.constraint,\n\t\t\tgiven: this.given,\n\t\t\texpected: this.expected,\n\t\t\tmessage: this.message\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst constraint = options.stylize(this.constraint, 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[ExpectedConstraintError: ${constraint}]`, 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1 };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\t\tconst given = inspect(this.given, newOptions).replace(/\\n/g, padding);\n\n\t\tconst header = `${options.stylize('ExpectedConstraintError', 'special')} > ${constraint}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst expectedBlock = `\\n  ${options.stylize('Expected: ', 'string')}${options.stylize(this.expected, 'boolean')}`;\n\t\tconst givenBlock = `\\n  ${options.stylize('Received:', 'regexp')}${padding}${given}`;\n\t\treturn `${header}\\n  ${message}\\n${expectedBlock}\\n${givenBlock}`;\n\t}\n}\n", "export class Result<T, E extends Error = Error> {\n\tpublic readonly success: boolean;\n\tpublic readonly value?: T;\n\tpublic readonly error?: E;\n\n\tprivate constructor(success: boolean, value?: T, error?: E) {\n\t\tthis.success = success;\n\t\tif (success) {\n\t\t\tthis.value = value;\n\t\t} else {\n\t\t\tthis.error = error;\n\t\t}\n\t}\n\n\tpublic isOk(): this is { success: true; value: T } {\n\t\treturn this.success;\n\t}\n\n\tpublic isErr(): this is { success: false; error: E } {\n\t\treturn !this.success;\n\t}\n\n\tpublic unwrap(): T {\n\t\tif (this.isOk()) return this.value;\n\t\tthrow this.error as Error;\n\t}\n\n\tpublic static ok<T, E extends Error = Error>(value: T): Result<T, E> {\n\t\treturn new Result<T, E>(true, value);\n\t}\n\n\tpublic static err<T, E extends Error = Error>(error: E): Result<T, E> {\n\t\treturn new Result<T, E>(false, undefined, error);\n\t}\n}\n", "let validationEnabled = true;\n\n/**\n * Sets whether validators should run on the input, or if the input should be passed through.\n * @param enabled Whether validation should be done on inputs\n */\nexport function setGlobalValidationEnabled(enabled: boolean) {\n\tvalidationEnabled = enabled;\n}\n\n/**\n * @returns Whether validation is enabled\n */\nexport function getGlobalValidationEnabled() {\n\treturn validationEnabled;\n}\n", "// https://github.com/microsoft/TypeScript/issues/37663\ntype Fn = (...args: unknown[]) => unknown;\n\nexport function getValue<T, U = T extends Fn ? ReturnType<T> : T>(valueOrFn: T): U {\n\treturn typeof valueOrFn === 'function' ? valueOrFn() : valueOrFn;\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport { whenConstraint, type WhenKey, type WhenOptions } from '../constraints/ObjectConstrains';\nimport { getGlobalValidationEnabled } from '../lib/configs';\nimport type { BaseConstraintError } from '../lib/errors/BaseConstraintError';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport type { CombinedError } from '../lib/errors/CombinedError';\nimport type { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport type { UnknownEnumValueError } from '../lib/errors/UnknownEnumValueError';\nimport type { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { InferResultType, ValidatorOptions } from '../lib/util-types';\nimport { ArrayValidator, DefaultValidator, LiteralValidator, NullishVali<PERSON>tor, SetValidator, UnionValidator } from './imports';\nimport { getValue } from './util/getValue';\n\nexport abstract class BaseValidator<T> {\n\tpublic description?: string;\n\tprotected validatorOptions: ValidatorOptions;\n\tprotected parent?: object;\n\tprotected constraints: readonly IConstraint<T>[] = [];\n\tprotected isValidationEnabled: boolean | (() => boolean) | null = null;\n\n\tpublic constructor(validatorOptions: ValidatorOptions = {}, constraints: readonly IConstraint<T>[] = []) {\n\t\tthis.constraints = constraints;\n\t\tthis.validatorOptions = validatorOptions;\n\t}\n\n\tpublic setParent(parent: object): this {\n\t\tthis.parent = parent;\n\t\treturn this;\n\t}\n\n\tpublic optional(options: ValidatorOptions = this.validatorOptions): UnionValidator<T | undefined> {\n\t\treturn new UnionValidator([new LiteralValidator(undefined, options), this.clone()], options);\n\t}\n\n\tpublic nullable(options: ValidatorOptions = this.validatorOptions): UnionValidator<T | null> {\n\t\treturn new UnionValidator([new LiteralValidator(null, options), this.clone()], options);\n\t}\n\n\tpublic nullish(options: ValidatorOptions = this.validatorOptions): UnionValidator<T | null | undefined> {\n\t\treturn new UnionValidator([new NullishValidator(options), this.clone()], options);\n\t}\n\n\tpublic array(options: ValidatorOptions = this.validatorOptions): ArrayValidator<T[]> {\n\t\treturn new ArrayValidator<T[]>(this.clone(), options);\n\t}\n\n\tpublic set(options: ValidatorOptions = this.validatorOptions): SetValidator<T> {\n\t\treturn new SetValidator<T>(this.clone(), options);\n\t}\n\n\tpublic or<O>(...predicates: readonly BaseValidator<O>[]): UnionValidator<T | O> {\n\t\treturn new UnionValidator<T | O>([this.clone(), ...predicates], this.validatorOptions);\n\t}\n\n\tpublic transform(cb: (value: T) => T, options?: ValidatorOptions): this;\n\tpublic transform<O>(cb: (value: T) => O, options?: ValidatorOptions): BaseValidator<O>;\n\tpublic transform<O>(cb: (value: T) => O, options: ValidatorOptions = this.validatorOptions): BaseValidator<O> {\n\t\treturn this.addConstraint(\n\t\t\t{\n\t\t\t\trun: (input) => Result.ok(cb(input) as unknown as T)\n\t\t\t},\n\t\t\toptions\n\t\t) as unknown as BaseValidator<O>;\n\t}\n\n\tpublic reshape(cb: (input: T) => Result<T>, options?: ValidatorOptions): this;\n\tpublic reshape<R extends Result<unknown>, O = InferResultType<R>>(cb: (input: T) => R, options?: ValidatorOptions): BaseValidator<O>;\n\tpublic reshape<R extends Result<unknown>, O = InferResultType<R>>(\n\t\tcb: (input: T) => R,\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): BaseValidator<O> {\n\t\treturn this.addConstraint(\n\t\t\t{\n\t\t\t\trun: cb as unknown as (input: T) => Result<T, BaseConstraintError<T>>\n\t\t\t},\n\t\t\toptions\n\t\t) as unknown as BaseValidator<O>;\n\t}\n\n\tpublic default(\n\t\tvalue: Exclude<T, undefined> | (() => Exclude<T, undefined>),\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): DefaultValidator<Exclude<T, undefined>> {\n\t\treturn new DefaultValidator(this.clone() as unknown as BaseValidator<Exclude<T, undefined>>, value, options);\n\t}\n\n\tpublic when<Key extends WhenKey, This extends BaseValidator<any> = this>(\n\t\tkey: Key,\n\t\toptions: WhenOptions<This, Key>,\n\t\tvalidatorOptions?: ValidatorOptions\n\t): this {\n\t\treturn this.addConstraint(whenConstraint<This, T, Key>(key, options, this as unknown as This, validatorOptions));\n\t}\n\n\tpublic describe(description: string): this {\n\t\tconst clone = this.clone();\n\t\tclone.description = description;\n\t\treturn clone;\n\t}\n\n\tpublic run(value: unknown): Result<T, BaseError> {\n\t\tlet result = this.handle(value) as Result<T, BaseError>;\n\t\tif (result.isErr()) return result;\n\n\t\tfor (const constraint of this.constraints) {\n\t\t\tresult = constraint.run(result.value as T, this.parent);\n\t\t\tif (result.isErr()) break;\n\t\t}\n\n\t\treturn result;\n\t}\n\n\tpublic parse<R extends T = T>(value: unknown): R {\n\t\t// If validation is disabled (at the validator or global level), we only run the `handle` method, which will do some basic checks\n\t\t// (like that the input is a string for a string validator)\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn this.handle(value).unwrap() as R;\n\t\t}\n\n\t\treturn this.constraints.reduce((v, constraint) => constraint.run(v).unwrap(), this.handle(value).unwrap()) as R;\n\t}\n\n\tpublic is<R extends T = T>(value: unknown): value is R {\n\t\treturn this.run(value).isOk();\n\t}\n\n\t/**\n\t * Sets if the validator should also run constraints or just do basic checks.\n\t * @param isValidationEnabled Whether this validator should be enabled or disabled. You can pass boolean or a function returning boolean which will be called just before parsing.\n\t * Set to `null` to go off of the global configuration.\n\t */\n\tpublic setValidationEnabled(isValidationEnabled: boolean | (() => boolean) | null): this {\n\t\tconst clone = this.clone();\n\t\tclone.isValidationEnabled = isValidationEnabled;\n\t\treturn clone;\n\t}\n\n\tpublic getValidationEnabled() {\n\t\treturn getValue(this.isValidationEnabled);\n\t}\n\n\tprotected get shouldRunConstraints(): boolean {\n\t\treturn getValue(this.isValidationEnabled) ?? getGlobalValidationEnabled();\n\t}\n\n\tprotected clone(): this {\n\t\tconst clone: this = Reflect.construct(this.constructor, [this.validatorOptions, this.constraints]);\n\t\tclone.isValidationEnabled = this.isValidationEnabled;\n\t\treturn clone;\n\t}\n\n\tprotected abstract handle(value: unknown): Result<T, ValidatorError>;\n\n\tprotected addConstraint(constraint: IConstraint<T>, validatorOptions: ValidatorOptions = this.validatorOptions): this {\n\t\tconst clone = this.clone();\n\t\tclone.validatorOptions = validatorOptions;\n\t\tclone.constraints = clone.constraints.concat(constraint);\n\t\treturn clone;\n\t}\n}\n\nexport type ValidatorError = ValidationError | CombinedError | CombinedPropertyError | UnknownEnumValueError;\n", "import fastDeepEqual from 'fast-deep-equal/es6/index.js';\nimport uniqWith from 'lodash/uniqWith.js';\n\nexport function isUnique(input: unknown[]) {\n\tif (input.length < 2) return true;\n\tconst uniqueArray = uniqWith(input, fastDeepEqual);\n\treturn uniqueArray.length === input.length;\n}\n", "export function lessThan(a: number, b: number): boolean;\nexport function lessThan(a: bigint, b: bigint): boolean;\nexport function lessThan(a: number | bigint, b: number | bigint): boolean {\n\treturn a < b;\n}\n\nexport function lessThanOrEqual(a: number, b: number): boolean;\nexport function lessThanOrEqual(a: bigint, b: bigint): boolean;\nexport function lessThanOrEqual(a: number | bigint, b: number | bigint): boolean {\n\treturn a <= b;\n}\n\nexport function greaterThan(a: number, b: number): boolean;\nexport function greaterThan(a: bigint, b: bigint): boolean;\nexport function greaterThan(a: number | bigint, b: number | bigint): boolean {\n\treturn a > b;\n}\n\nexport function greaterThanOrEqual(a: number, b: number): boolean;\nexport function greaterThanOrEqual(a: bigint, b: bigint): boolean;\nexport function greaterThanOrEqual(a: number | bigint, b: number | bigint): boolean {\n\treturn a >= b;\n}\n\nexport function equal(a: number, b: number): boolean;\nexport function equal(a: bigint, b: bigint): boolean;\nexport function equal(a: number | bigint, b: number | bigint): boolean {\n\treturn a === b;\n}\n\nexport function notEqual(a: number, b: number): boolean;\nexport function notEqual(a: bigint, b: bigint): boolean;\nexport function notEqual(a: number | bigint, b: number | bigint): boolean {\n\treturn a !== b;\n}\n\nexport interface Comparator {\n\t(a: number, b: number): boolean;\n\t(a: bigint, b: bigint): boolean;\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport type { IConstraint } from './base/IConstraint';\nimport { isUnique } from './util/isUnique';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\n\nexport type ArrayConstraintName = `s.array(T).${\n\t| 'unique'\n\t| `length${\n\t\t\t| 'LessThan'\n\t\t\t| 'LessThanOrEqual'\n\t\t\t| 'GreaterThan'\n\t\t\t| 'GreaterThanOrEqual'\n\t\t\t| 'Equal'\n\t\t\t| 'NotEqual'\n\t\t\t| 'Range'\n\t\t\t| 'RangeInclusive'\n\t\t\t| 'RangeExclusive'}`}()`;\n\nfunction arrayLengthComparator<T>(\n\tcomparator: Comparator,\n\tname: ArrayConstraintName,\n\texpected: string,\n\tlength: number,\n\toptions?: ValidatorOptions\n): IConstraint<T[]> {\n\treturn {\n\t\trun(input: T[]) {\n\t\t\treturn comparator(input.length, length) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, options?.message ?? 'Invalid Array length', input, expected));\n\t\t}\n\t};\n}\n\nexport function arrayLengthLessThan<T>(value: number, options?: ValidatorOptions): IConstraint<T[]> {\n\tconst expected = `expected.length < ${value}`;\n\treturn arrayLengthComparator(lessThan, 's.array(T).lengthLessThan()', expected, value, options);\n}\n\nexport function arrayLengthLessThanOrEqual<T>(value: number, options?: ValidatorOptions): IConstraint<T[]> {\n\tconst expected = `expected.length <= ${value}`;\n\treturn arrayLengthComparator(lessThanOrEqual, 's.array(T).lengthLessThanOrEqual()', expected, value, options);\n}\n\nexport function arrayLengthGreaterThan<T>(value: number, options?: ValidatorOptions): IConstraint<T[]> {\n\tconst expected = `expected.length > ${value}`;\n\treturn arrayLengthComparator(greaterThan, 's.array(T).lengthGreaterThan()', expected, value, options);\n}\n\nexport function arrayLengthGreaterThanOrEqual<T>(value: number, options?: ValidatorOptions): IConstraint<T[]> {\n\tconst expected = `expected.length >= ${value}`;\n\treturn arrayLengthComparator(greaterThanOrEqual, 's.array(T).lengthGreaterThanOrEqual()', expected, value, options);\n}\n\nexport function arrayLengthEqual<T>(value: number, options?: ValidatorOptions): IConstraint<T[]> {\n\tconst expected = `expected.length === ${value}`;\n\treturn arrayLengthComparator(equal, 's.array(T).lengthEqual()', expected, value, options);\n}\n\nexport function arrayLengthNotEqual<T>(value: number, options?: ValidatorOptions): IConstraint<T[]> {\n\tconst expected = `expected.length !== ${value}`;\n\treturn arrayLengthComparator(notEqual, 's.array(T).lengthNotEqual()', expected, value, options);\n}\n\nexport function arrayLengthRange<T>(start: number, endBefore: number, options?: ValidatorOptions): IConstraint<T[]> {\n\tconst expected = `expected.length >= ${start} && expected.length < ${endBefore}`;\n\treturn {\n\t\trun(input: T[]) {\n\t\t\treturn input.length >= start && input.length < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.array(T).lengthRange()', options?.message ?? 'Invalid Array length', input, expected));\n\t\t}\n\t};\n}\n\nexport function arrayLengthRangeInclusive<T>(start: number, end: number, options?: ValidatorOptions): IConstraint<T[]> {\n\tconst expected = `expected.length >= ${start} && expected.length <= ${end}`;\n\treturn {\n\t\trun(input: T[]) {\n\t\t\treturn input.length >= start && input.length <= end //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError('s.array(T).lengthRangeInclusive()', options?.message ?? 'Invalid Array length', input, expected)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nexport function arrayLengthRangeExclusive<T>(startAfter: number, endBefore: number, options?: ValidatorOptions): IConstraint<T[]> {\n\tconst expected = `expected.length > ${startAfter} && expected.length < ${endBefore}`;\n\treturn {\n\t\trun(input: T[]) {\n\t\t\treturn input.length > startAfter && input.length < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError('s.array(T).lengthRangeExclusive()', options?.message ?? 'Invalid Array length', input, expected)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nexport function uniqueArray(options?: ValidatorOptions): IConstraint<unknown[]> {\n\treturn {\n\t\trun(input: unknown[]) {\n\t\t\treturn isUnique(input) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.array(T).unique()',\n\t\t\t\t\t\t\toptions?.message ?? 'Array values are not unique',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\t'Expected all values to be unique'\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n", "import type { InspectOptionsStylized } from 'util';\nimport type { ValidatorOptions } from '../util-types';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\n\nexport class CombinedPropertyError extends BaseError {\n\tpublic readonly errors: [PropertyKey, BaseError][];\n\n\tpublic constructor(errors: [PropertyKey, BaseError][], validatorOptions?: ValidatorOptions) {\n\t\tsuper(validatorOptions?.message ?? 'Received one or more errors');\n\n\t\tthis.errors = errors;\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize('[CombinedPropertyError]', 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1, compact: true };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\n\t\tconst header = `${options.stylize('CombinedPropertyError', 'special')} (${options.stylize(this.errors.length.toString(), 'number')})`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst errors = this.errors\n\t\t\t.map(([key, error]) => {\n\t\t\t\tconst property = CombinedPropertyError.formatProperty(key, options);\n\t\t\t\tconst body = error[customInspectSymbolStackLess](depth - 1, newOptions).replace(/\\n/g, padding);\n\n\t\t\t\treturn `  input${property}${padding}${body}`;\n\t\t\t})\n\t\t\t.join('\\n\\n');\n\t\treturn `${header}\\n  ${message}\\n\\n${errors}`;\n\t}\n\n\tprivate static formatProperty(key: PropertyKey, options: InspectOptionsStylized): string {\n\t\tif (typeof key === 'string') return options.stylize(`.${key}`, 'symbol');\n\t\tif (typeof key === 'number') return `[${options.stylize(key.toString(), 'number')}]`;\n\t\treturn `[${options.stylize('Symbol', 'symbol')}(${key.description})]`;\n\t}\n}\n", "import { inspect, type InspectOptionsStylized } from 'util';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\nimport type { ValidationErrorJsonified } from './error-types';\n\nexport class ValidationError extends BaseError {\n\tpublic readonly validator: string;\n\tpublic readonly given: unknown;\n\n\tpublic constructor(validator: string, message: string, given: unknown) {\n\t\tsuper(message);\n\n\t\tthis.validator = validator;\n\t\tthis.given = given;\n\t}\n\n\tpublic override toJSON(): ValidationErrorJsonified {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tmessage: 'Unknown validation error occurred.',\n\t\t\tvalidator: this.validator,\n\t\t\tgiven: this.given\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst validator = options.stylize(this.validator, 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[ValidationError: ${validator}]`, 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1, compact: true };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\t\tconst given = inspect(this.given, newOptions).replace(/\\n/g, padding);\n\n\t\tconst header = `${options.stylize('ValidationError', 'special')} > ${validator}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst givenBlock = `\\n  ${options.stylize('Received:', 'regexp')}${padding}${given}`;\n\t\treturn `${header}\\n  ${message}\\n${givenBlock}`;\n\t}\n}\n", "import {\n\tarrayLengthEqual,\n\tarrayLengthGreater<PERSON>han,\n\tarrayLengthGreaterThanOrEqual,\n\tarrayLengthLessThan,\n\tarrayLengthLessThanOrEqual,\n\tarrayLengthNotEqual,\n\tarrayLengthRange,\n\tarrayLengthRangeExclusive,\n\tarrayLengthRangeInclusive,\n\tuniqueArray\n} from '../constraints/ArrayConstraints';\nimport type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ExpandSmallerTuples, Tuple, UnshiftTuple, ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class ArrayValidator<T extends unknown[], I = T[number]> extends BaseValidator<T> {\n\tprivate readonly validator: BaseValidator<I>;\n\n\tpublic constructor(validator: BaseValidator<I>, validatorOptions: ValidatorOptions = {}, constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.validator = validator;\n\t}\n\n\tpublic lengthLessThan<N extends number>(\n\t\tlength: N,\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): ArrayValidator<ExpandSmallerTuples<UnshiftTuple<[...Tuple<I, N>]>>> {\n\t\treturn this.addConstraint(arrayLengthLessThan(length, options) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthLessThanOrEqual<N extends number>(\n\t\tlength: N,\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): ArrayValidator<ExpandSmallerTuples<[...Tuple<I, N>]>> {\n\t\treturn this.addConstraint(arrayLengthLessThanOrEqual(length, options) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthGreaterThan<N extends number>(\n\t\tlength: N,\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): ArrayValidator<[...Tuple<I, N>, I, ...T]> {\n\t\treturn this.addConstraint(arrayLengthGreaterThan(length, options) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthGreaterThanOrEqual<N extends number>(\n\t\tlength: N,\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): ArrayValidator<[...Tuple<I, N>, ...T]> {\n\t\treturn this.addConstraint(arrayLengthGreaterThanOrEqual(length, options) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthEqual<N extends number>(length: N, options: ValidatorOptions = this.validatorOptions): ArrayValidator<[...Tuple<I, N>]> {\n\t\treturn this.addConstraint(arrayLengthEqual(length, options) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthNotEqual<N extends number>(length: N, options: ValidatorOptions = this.validatorOptions): ArrayValidator<[...Tuple<I, N>]> {\n\t\treturn this.addConstraint(arrayLengthNotEqual(length, options) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthRange<S extends number, E extends number>(\n\t\tstart: S,\n\t\tendBefore: E,\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): ArrayValidator<Exclude<ExpandSmallerTuples<UnshiftTuple<[...Tuple<I, E>]>>, ExpandSmallerTuples<UnshiftTuple<[...Tuple<I, S>]>>>> {\n\t\treturn this.addConstraint(arrayLengthRange(start, endBefore, options) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthRangeInclusive<S extends number, E extends number>(\n\t\tstartAt: S,\n\t\tendAt: E,\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): ArrayValidator<Exclude<ExpandSmallerTuples<[...Tuple<I, E>]>, ExpandSmallerTuples<UnshiftTuple<[...Tuple<I, S>]>>>> {\n\t\treturn this.addConstraint(arrayLengthRangeInclusive(startAt, endAt, options) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthRangeExclusive<S extends number, E extends number>(\n\t\tstartAfter: S,\n\t\tendBefore: E,\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): ArrayValidator<Exclude<ExpandSmallerTuples<UnshiftTuple<[...Tuple<I, E>]>>, ExpandSmallerTuples<[...Tuple<T, S>]>>> {\n\t\treturn this.addConstraint(arrayLengthRangeExclusive(startAfter, endBefore, options) as IConstraint<T>) as any;\n\t}\n\n\tpublic unique(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(uniqueArray(options) as IConstraint<T>);\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validator, this.validatorOptions, this.constraints]);\n\t}\n\n\tprotected handle(values: unknown): Result<T, ValidationError | CombinedPropertyError> {\n\t\tif (!Array.isArray(values)) {\n\t\t\treturn Result.err(new ValidationError('s.array(T)', this.validatorOptions.message ?? 'Expected an array', values));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(values as T);\n\t\t}\n\n\t\tconst errors: [number, BaseError][] = [];\n\t\tconst transformed: T = [] as unknown as T;\n\n\t\tfor (let i = 0; i < values.length; i++) {\n\t\t\tconst result = this.validator.run(values[i]);\n\t\t\tif (result.isOk()) transformed.push(result.value);\n\t\t\telse errors.push([i, result.error!]);\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(transformed)\n\t\t\t: Result.err(new CombinedPropertyError(errors, this.validatorOptions));\n\t}\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport type { IConstraint } from './base/IConstraint';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\n\nexport type BigIntConstraintName = `s.bigint().${\n\t| 'lessThan'\n\t| 'lessThanOrEqual'\n\t| 'greaterThan'\n\t| 'greaterThanOrEqual'\n\t| 'equal'\n\t| 'notEqual'\n\t| 'divisibleBy'}()`;\n\nfunction bigintComparator(\n\tcomparator: Comparator,\n\tname: BigIntConstraintName,\n\texpected: string,\n\tnumber: bigint,\n\toptions?: ValidatorOptions\n): IConstraint<bigint> {\n\treturn {\n\t\trun(input: bigint) {\n\t\t\treturn comparator(input, number) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, options?.message ?? 'Invalid bigint value', input, expected));\n\t\t}\n\t};\n}\n\nexport function bigintLessThan(value: bigint, options?: ValidatorOptions): IConstraint<bigint> {\n\tconst expected = `expected < ${value}n`;\n\treturn bigintComparator(lessThan, 's.bigint().lessThan()', expected, value, options);\n}\n\nexport function bigintLessThanOrEqual(value: bigint, options?: ValidatorOptions): IConstraint<bigint> {\n\tconst expected = `expected <= ${value}n`;\n\treturn bigintComparator(lessThanOrEqual, 's.bigint().lessThanOrEqual()', expected, value, options);\n}\n\nexport function bigintGreaterThan(value: bigint, options?: ValidatorOptions): IConstraint<bigint> {\n\tconst expected = `expected > ${value}n`;\n\treturn bigintComparator(greaterThan, 's.bigint().greaterThan()', expected, value, options);\n}\n\nexport function bigintGreaterThanOrEqual(value: bigint, options?: ValidatorOptions): IConstraint<bigint> {\n\tconst expected = `expected >= ${value}n`;\n\treturn bigintComparator(greaterThanOrEqual, 's.bigint().greaterThanOrEqual()', expected, value, options);\n}\n\nexport function bigintEqual(value: bigint, options?: ValidatorOptions): IConstraint<bigint> {\n\tconst expected = `expected === ${value}n`;\n\treturn bigintComparator(equal, 's.bigint().equal()', expected, value, options);\n}\n\nexport function bigintNotEqual(value: bigint, options?: ValidatorOptions): IConstraint<bigint> {\n\tconst expected = `expected !== ${value}n`;\n\treturn bigintComparator(notEqual, 's.bigint().notEqual()', expected, value, options);\n}\n\nexport function bigintDivisibleBy(divider: bigint, options?: ValidatorOptions): IConstraint<bigint> {\n\tconst expected = `expected % ${divider}n === 0n`;\n\treturn {\n\t\trun(input: bigint) {\n\t\t\treturn input % divider === 0n //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.bigint().divisibleBy()', options?.message ?? 'BigInt is not divisible', input, expected));\n\t\t}\n\t};\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport {\n\tbigintDivisibleBy,\n\tbigintEqual,\n\tbigintGreaterThan,\n\tbigintGreaterThanOrEqual,\n\tbigintLessThan,\n\tbigintLessThanOrEqual,\n\tbigintNotEqual\n} from '../constraints/BigIntConstraints';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class BigIntValidator<T extends bigint> extends BaseValidator<T> {\n\tpublic lessThan(number: bigint, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(bigintLessThan(number, options) as IConstraint<T>);\n\t}\n\n\tpublic lessThanOrEqual(number: bigint, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(bigintLessThanOrEqual(number, options) as IConstraint<T>);\n\t}\n\n\tpublic greaterThan(number: bigint, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(bigintGreaterThan(number, options) as IConstraint<T>);\n\t}\n\n\tpublic greaterThanOrEqual(number: bigint, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(bigintGreaterThanOrEqual(number, options) as IConstraint<T>);\n\t}\n\n\tpublic equal<N extends bigint>(number: N, options: ValidatorOptions = this.validatorOptions): BigIntValidator<N> {\n\t\treturn this.addConstraint(bigintEqual(number, options) as IConstraint<T>) as unknown as BigIntValidator<N>;\n\t}\n\n\tpublic notEqual(number: bigint, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(bigintNotEqual(number, options) as IConstraint<T>);\n\t}\n\n\tpublic positive(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.greaterThanOrEqual(0n, options);\n\t}\n\n\tpublic negative(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.lessThan(0n, options);\n\t}\n\n\tpublic divisibleBy(number: bigint, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(bigintDivisibleBy(number, options) as IConstraint<T>);\n\t}\n\n\tpublic abs(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.transform((value) => (value < 0 ? -value : value) as T, options);\n\t}\n\n\tpublic intN(bits: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.transform((value) => BigInt.asIntN(bits, value) as T, options);\n\t}\n\n\tpublic uintN(bits: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.transform((value) => BigInt.asUintN(bits, value) as T, options);\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn typeof value === 'bigint' //\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ValidationError('s.bigint()', this.validatorOptions.message ?? 'Expected a bigint primitive', value));\n\t}\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport type { IConstraint } from './base/IConstraint';\n\nexport type BooleanConstraintName = `s.boolean().${boolean}()`;\n\nexport function booleanTrue(options?: ValidatorOptions): IConstraint<boolean, true> {\n\treturn {\n\t\trun(input: boolean) {\n\t\t\treturn input //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.boolean().true()', options?.message ?? 'Invalid boolean value', input, 'true'));\n\t\t}\n\t};\n}\n\nexport function booleanFalse(options?: ValidatorOptions): IConstraint<boolean, false> {\n\treturn {\n\t\trun(input: boolean) {\n\t\t\treturn input //\n\t\t\t\t? Result.err(new ExpectedConstraintError('s.boolean().false()', options?.message ?? 'Invalid boolean value', input, 'false'))\n\t\t\t\t: Result.ok(input);\n\t\t}\n\t};\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport { booleanFalse, booleanTrue } from '../constraints/BooleanConstraints';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class BooleanValidator<T extends boolean = boolean> extends BaseValidator<T> {\n\tpublic true(options: ValidatorOptions = this.validatorOptions): BooleanValidator<true> {\n\t\treturn this.addConstraint(booleanTrue(options) as IConstraint<T>) as BooleanValidator<true>;\n\t}\n\n\tpublic false(options: ValidatorOptions = this.validatorOptions): BooleanValidator<false> {\n\t\treturn this.addConstraint(booleanFalse(options) as IConstraint<T>) as BooleanValidator<false>;\n\t}\n\n\tpublic equal<R extends true | false>(value: R, options: ValidatorOptions = this.validatorOptions): BooleanValidator<R> {\n\t\treturn (value ? this.true(options) : this.false(options)) as BooleanValidator<R>;\n\t}\n\n\tpublic notEqual<R extends true | false>(value: R, options: ValidatorOptions = this.validatorOptions): BooleanValidator<R> {\n\t\treturn (value ? this.false(options) : this.true(options)) as BooleanValidator<R>;\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn typeof value === 'boolean' //\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ValidationError('s.boolean()', this.validatorOptions.message ?? 'Expected a boolean primitive', value));\n\t}\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport type { IConstraint } from './base/IConstraint';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\n\nexport type DateConstraintName = `s.date().${\n\t| 'lessThan'\n\t| 'lessThanOrEqual'\n\t| 'greaterThan'\n\t| 'greaterThanOrEqual'\n\t| 'equal'\n\t| 'notEqual'\n\t| 'valid'\n\t| 'invalid'}()`;\n\nfunction dateComparator(\n\tcomparator: Comparator,\n\tname: DateConstraintName,\n\texpected: string,\n\tnumber: number,\n\toptions?: ValidatorOptions\n): IConstraint<Date> {\n\treturn {\n\t\trun(input: Date) {\n\t\t\treturn comparator(input.getTime(), number) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, options?.message ?? 'Invalid Date value', input, expected));\n\t\t}\n\t};\n}\n\nexport function dateLessThan(value: Date, options?: ValidatorOptions): IConstraint<Date> {\n\tconst expected = `expected < ${value.toISOString()}`;\n\treturn dateComparator(lessThan, 's.date().lessThan()', expected, value.getTime(), options);\n}\n\nexport function dateLessThanOrEqual(value: Date, options?: ValidatorOptions): IConstraint<Date> {\n\tconst expected = `expected <= ${value.toISOString()}`;\n\treturn dateComparator(lessThanOrEqual, 's.date().lessThanOrEqual()', expected, value.getTime(), options);\n}\n\nexport function dateGreaterThan(value: Date, options?: ValidatorOptions): IConstraint<Date> {\n\tconst expected = `expected > ${value.toISOString()}`;\n\treturn dateComparator(greaterThan, 's.date().greaterThan()', expected, value.getTime(), options);\n}\n\nexport function dateGreaterThanOrEqual(value: Date, options?: ValidatorOptions): IConstraint<Date> {\n\tconst expected = `expected >= ${value.toISOString()}`;\n\treturn dateComparator(greaterThanOrEqual, 's.date().greaterThanOrEqual()', expected, value.getTime(), options);\n}\n\nexport function dateEqual(value: Date, options?: ValidatorOptions): IConstraint<Date> {\n\tconst expected = `expected === ${value.toISOString()}`;\n\treturn dateComparator(equal, 's.date().equal()', expected, value.getTime(), options);\n}\n\nexport function dateNotEqual(value: Date, options?: ValidatorOptions): IConstraint<Date> {\n\tconst expected = `expected !== ${value.toISOString()}`;\n\treturn dateComparator(notEqual, 's.date().notEqual()', expected, value.getTime(), options);\n}\n\nexport function dateInvalid(options?: ValidatorOptions): IConstraint<Date> {\n\treturn {\n\t\trun(input: Date) {\n\t\t\treturn Number.isNaN(input.getTime()) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.date().invalid()', options?.message ?? 'Invalid Date value', input, 'expected === NaN'));\n\t\t}\n\t};\n}\n\nexport function dateValid(options?: ValidatorOptions): IConstraint<Date> {\n\treturn {\n\t\trun(input: Date) {\n\t\t\treturn Number.isNaN(input.getTime()) //\n\t\t\t\t? Result.err(new ExpectedConstraintError('s.date().valid()', options?.message ?? 'Invalid Date value', input, 'expected !== NaN'))\n\t\t\t\t: Result.ok(input);\n\t\t}\n\t};\n}\n", "import {\n\tdateEqual,\n\tdateGreater<PERSON>han,\n\tdateGreaterThanOrEqual,\n\tdateInvalid,\n\tdateLessThan,\n\tdateLessThanOrEqual,\n\tdateNotEqual,\n\tdateValid\n} from '../constraints/DateConstraints';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class DateValidator extends BaseValidator<Date> {\n\tpublic lessThan(date: Date | number | string, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(dateLessThan(new Date(date), options));\n\t}\n\n\tpublic lessThanOrEqual(date: Date | number | string, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(dateLessThanOrEqual(new Date(date), options));\n\t}\n\n\tpublic greaterThan(date: Date | number | string, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(dateGreaterThan(new Date(date), options));\n\t}\n\n\tpublic greaterThanOrEqual(date: Date | number | string, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(dateGreaterThanOrEqual(new Date(date), options));\n\t}\n\n\tpublic equal(date: Date | number | string, options: ValidatorOptions = this.validatorOptions): this {\n\t\tconst resolved = new Date(date);\n\t\treturn Number.isNaN(resolved.getTime()) //\n\t\t\t? this.invalid(options)\n\t\t\t: this.addConstraint(dateEqual(resolved, options));\n\t}\n\n\tpublic notEqual(date: Date | number | string, options: ValidatorOptions = this.validatorOptions): this {\n\t\tconst resolved = new Date(date);\n\t\treturn Number.isNaN(resolved.getTime()) //\n\t\t\t? this.valid(options)\n\t\t\t: this.addConstraint(dateNotEqual(resolved, options));\n\t}\n\n\tpublic valid(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(dateValid(options));\n\t}\n\n\tpublic invalid(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(dateInvalid(options));\n\t}\n\n\tprotected handle(value: unknown): Result<Date, ValidationError> {\n\t\treturn value instanceof Date //\n\t\t\t? Result.ok(value)\n\t\t\t: Result.err(new ValidationError('s.date()', this.validatorOptions.message ?? 'Expected a Date', value));\n\t}\n}\n", "import { inspect, type InspectOptionsStylized } from 'util';\nimport { customInspectSymbolStackLess } from './BaseError';\nimport type { ExpectedValidationErrorJsonified } from './error-types';\nimport { ValidationError } from './ValidationError';\n\nexport class ExpectedValidationError<T> extends ValidationError {\n\tpublic readonly expected: T;\n\n\tpublic constructor(validator: string, message: string, given: unknown, expected: T) {\n\t\tsuper(validator, message, given);\n\t\tthis.expected = expected;\n\t}\n\n\tpublic override toJSON(): ExpectedValidationErrorJsonified<T> {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tvalidator: this.validator,\n\t\t\tgiven: this.given,\n\t\t\texpected: this.expected,\n\t\t\tmessage: this.message\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst validator = options.stylize(this.validator, 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[ExpectedValidationError: ${validator}]`, 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1 };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\t\tconst expected = inspect(this.expected, newOptions).replace(/\\n/g, padding);\n\t\tconst given = inspect(this.given, newOptions).replace(/\\n/g, padding);\n\n\t\tconst header = `${options.stylize('ExpectedValidationError', 'special')} > ${validator}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst expectedBlock = `\\n  ${options.stylize('Expected:', 'string')}${padding}${expected}`;\n\t\tconst givenBlock = `\\n  ${options.stylize('Received:', 'regexp')}${padding}${given}`;\n\t\treturn `${header}\\n  ${message}\\n${expectedBlock}\\n${givenBlock}`;\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport { ExpectedValidationError } from '../lib/errors/ExpectedValidationError';\nimport { Result } from '../lib/Result';\nimport type { Constructor, ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class InstanceValidator<T> extends BaseValidator<T> {\n\tpublic readonly expected: Constructor<T>;\n\n\tpublic constructor(expected: Constructor<T>, validatorOptions: ValidatorOptions = {}, constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.expected = expected;\n\t}\n\n\tprotected handle(value: unknown): Result<T, ExpectedValidationError<Constructor<T>>> {\n\t\treturn value instanceof this.expected //\n\t\t\t? Result.ok(value)\n\t\t\t: Result.err(new ExpectedValidationError('s.instance(V)', this.validatorOptions.message ?? 'Expected', value, this.expected));\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.expected, this.validatorOptions, this.constraints]);\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport { ExpectedValidationError } from '../lib/errors/ExpectedValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class LiteralValidator<T> extends BaseValidator<T> {\n\tpublic readonly expected: T;\n\n\tpublic constructor(literal: T, validatorOptions: ValidatorOptions = {}, constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.expected = literal;\n\t}\n\n\tprotected handle(value: unknown): Result<T, ExpectedValidationError<T>> {\n\t\treturn Object.is(value, this.expected) //\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(\n\t\t\t\t\tnew ExpectedValidationError('s.literal(V)', this.validatorOptions.message ?? 'Expected values to be equals', value, this.expected)\n\t\t\t\t);\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.expected, this.validatorOptions, this.constraints]);\n\t}\n}\n", "import { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class NeverValidator extends BaseValidator<never> {\n\tprotected handle(value: unknown): Result<never, ValidationError> {\n\t\treturn Result.err(new ValidationError('s.never()', this.validatorOptions.message ?? 'Expected a value to not be passed', value));\n\t}\n}\n", "import { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class NullishValidator extends BaseValidator<undefined | null> {\n\tprotected handle(value: unknown): Result<undefined | null, ValidationError> {\n\t\treturn value === undefined || value === null //\n\t\t\t? Result.ok(value)\n\t\t\t: Result.err(new ValidationError('s.nullish()', this.validatorOptions.message ?? 'Expected undefined or null', value));\n\t}\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\n\nimport type { IConstraint } from './base/IConstraint';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\n\nexport type NumberConstraintName = `s.number().${\n\t| 'lessThan'\n\t| 'lessThanOrEqual'\n\t| 'greaterThan'\n\t| 'greaterThanOrEqual'\n\t| 'equal'\n\t| 'equal'\n\t| 'notEqual'\n\t| 'notEqual'\n\t| 'int'\n\t| 'safeInt'\n\t| 'finite'\n\t| 'divisibleBy'}(${string})`;\n\nfunction numberComparator(\n\tcomparator: Comparator,\n\tname: NumberConstraintName,\n\texpected: string,\n\tnumber: number,\n\toptions?: ValidatorOptions\n): IConstraint<number> {\n\treturn {\n\t\trun(input: number) {\n\t\t\treturn comparator(input, number) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, options?.message ?? 'Invalid number value', input, expected));\n\t\t}\n\t};\n}\n\nexport function numberLessThan(value: number, options?: ValidatorOptions): IConstraint<number> {\n\tconst expected = `expected < ${value}`;\n\treturn numberComparator(lessThan, 's.number().lessThan()', expected, value, options);\n}\n\nexport function numberLessThanOrEqual(value: number, options?: ValidatorOptions): IConstraint<number> {\n\tconst expected = `expected <= ${value}`;\n\treturn numberComparator(lessThanOrEqual, 's.number().lessThanOrEqual()', expected, value, options);\n}\n\nexport function numberGreaterThan(value: number, options?: ValidatorOptions): IConstraint<number> {\n\tconst expected = `expected > ${value}`;\n\treturn numberComparator(greaterThan, 's.number().greaterThan()', expected, value, options);\n}\n\nexport function numberGreaterThanOrEqual(value: number, options?: ValidatorOptions): IConstraint<number> {\n\tconst expected = `expected >= ${value}`;\n\treturn numberComparator(greaterThanOrEqual, 's.number().greaterThanOrEqual()', expected, value, options);\n}\n\nexport function numberEqual(value: number, options?: ValidatorOptions): IConstraint<number> {\n\tconst expected = `expected === ${value}`;\n\treturn numberComparator(equal, 's.number().equal()', expected, value, options);\n}\n\nexport function numberNotEqual(value: number, options?: ValidatorOptions): IConstraint<number> {\n\tconst expected = `expected !== ${value}`;\n\treturn numberComparator(notEqual, 's.number().notEqual()', expected, value, options);\n}\n\nexport function numberInt(options?: ValidatorOptions): IConstraint<number> {\n\treturn {\n\t\trun(input: number) {\n\t\t\treturn Number.isInteger(input) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.number().int()',\n\t\t\t\t\t\t\toptions?.message ?? 'Given value is not an integer',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\t'Number.isInteger(expected) to be true'\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nexport function numberSafeInt(options?: ValidatorOptions): IConstraint<number> {\n\treturn {\n\t\trun(input: number) {\n\t\t\treturn Number.isSafeInteger(input) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.number().safeInt()',\n\t\t\t\t\t\t\toptions?.message ?? 'Given value is not a safe integer',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\t'Number.isSafeInteger(expected) to be true'\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nexport function numberFinite(options?: ValidatorOptions): IConstraint<number> {\n\treturn {\n\t\trun(input: number) {\n\t\t\treturn Number.isFinite(input) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.number().finite()',\n\t\t\t\t\t\t\toptions?.message ?? 'Given value is not finite',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\t'Number.isFinite(expected) to be true'\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nexport function numberNaN(options?: ValidatorOptions): IConstraint<number> {\n\treturn {\n\t\trun(input: number) {\n\t\t\treturn Number.isNaN(input) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError('s.number().equal(NaN)', options?.message ?? 'Invalid number value', input, 'expected === NaN')\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nexport function numberNotNaN(options?: ValidatorOptions): IConstraint<number> {\n\treturn {\n\t\trun(input: number) {\n\t\t\treturn Number.isNaN(input) //\n\t\t\t\t? Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError('s.number().notEqual(NaN)', options?.message ?? 'Invalid number value', input, 'expected !== NaN')\n\t\t\t\t\t)\n\t\t\t\t: Result.ok(input);\n\t\t}\n\t};\n}\n\nexport function numberDivisibleBy(divider: number, options?: ValidatorOptions): IConstraint<number> {\n\tconst expected = `expected % ${divider} === 0`;\n\treturn {\n\t\trun(input: number) {\n\t\t\treturn input % divider === 0 //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.number().divisibleBy()', options?.message ?? 'Number is not divisible', input, expected));\n\t\t}\n\t};\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport {\n\tnumberDivisibleBy,\n\tnumberEqual,\n\tnumberFinite,\n\tnumberGreaterThan,\n\tnumberGreaterThanOrEqual,\n\tnumberInt,\n\tnumberLessThan,\n\tnumberLessThanOrEqual,\n\tnumberNaN,\n\tnumberNotEqual,\n\tnumberNotNaN,\n\tnumberSafeInt\n} from '../constraints/NumberConstraints';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class NumberValidator<T extends number> extends BaseValidator<T> {\n\tpublic lessThan(number: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(numberLessThan(number, options) as IConstraint<T>);\n\t}\n\n\tpublic lessThanOrEqual(number: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(numberLessThanOrEqual(number, options) as IConstraint<T>);\n\t}\n\n\tpublic greaterThan(number: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(numberGreaterThan(number, options) as IConstraint<T>);\n\t}\n\n\tpublic greaterThanOrEqual(number: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(numberGreaterThanOrEqual(number, options) as IConstraint<T>);\n\t}\n\n\tpublic equal<N extends number>(number: N, options: ValidatorOptions = this.validatorOptions): NumberValidator<N> {\n\t\treturn Number.isNaN(number) //\n\t\t\t? (this.addConstraint(numberNaN(options) as IConstraint<T>) as unknown as NumberValidator<N>)\n\t\t\t: (this.addConstraint(numberEqual(number, options) as IConstraint<T>) as unknown as NumberValidator<N>);\n\t}\n\n\tpublic notEqual(number: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn Number.isNaN(number) //\n\t\t\t? this.addConstraint(numberNotNaN(options) as IConstraint<T>)\n\t\t\t: this.addConstraint(numberNotEqual(number, options) as IConstraint<T>);\n\t}\n\n\tpublic int(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(numberInt(options) as IConstraint<T>);\n\t}\n\n\tpublic safeInt(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(numberSafeInt(options) as IConstraint<T>);\n\t}\n\n\tpublic finite(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(numberFinite(options) as IConstraint<T>);\n\t}\n\n\tpublic positive(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.greaterThanOrEqual(0, options);\n\t}\n\n\tpublic negative(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.lessThan(0, options);\n\t}\n\n\tpublic divisibleBy(divider: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(numberDivisibleBy(divider, options) as IConstraint<T>);\n\t}\n\n\tpublic abs(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.transform(Math.abs as (value: number) => T, options);\n\t}\n\n\tpublic sign(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.transform(Math.sign as (value: number) => T, options);\n\t}\n\n\tpublic trunc(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.transform(Math.trunc as (value: number) => T, options);\n\t}\n\n\tpublic floor(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.transform(Math.floor as (value: number) => T, options);\n\t}\n\n\tpublic fround(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.transform(Math.fround as (value: number) => T, options);\n\t}\n\n\tpublic round(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.transform(Math.round as (value: number) => T, options);\n\t}\n\n\tpublic ceil(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.transform(Math.ceil as (value: number) => T, options);\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn typeof value === 'number' //\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ValidationError('s.number()', this.validatorOptions.message ?? 'Expected a number primitive', value));\n\t}\n}\n", "import type { InspectOptionsStylized } from 'util';\nimport type { ValidatorOptions } from '../util-types';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\nimport type { MissingPropertyErrorJsonified } from './error-types';\n\nexport class MissingPropertyError extends BaseError {\n\tpublic readonly property: PropertyKey;\n\n\tpublic constructor(property: PropertyKey, validatorOptions?: ValidatorOptions) {\n\t\tsuper(validatorOptions?.message ?? 'A required property is missing');\n\t\tthis.property = property;\n\t}\n\n\tpublic override toJSON(): MissingPropertyErrorJsonified {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tmessage: this.message,\n\t\t\tproperty: this.property\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst property = options.stylize(this.property.toString(), 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[MissingPropertyError: ${property}]`, 'special');\n\t\t}\n\n\t\tconst header = `${options.stylize('MissingPropertyError', 'special')} > ${property}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\treturn `${header}\\n  ${message}`;\n\t}\n}\n", "import { inspect, type InspectOptionsStylized } from 'util';\nimport type { ValidatorOptions } from '../util-types';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\nimport type { UnknownEnumKeyErrorJsonified } from './error-types';\n\nexport class UnknownPropertyError extends BaseError {\n\tpublic readonly property: PropertyKey;\n\tpublic readonly value: unknown;\n\n\tpublic constructor(property: PropertyKey, value: unknown, options?: ValidatorOptions) {\n\t\tsuper(options?.message ?? 'Received unexpected property');\n\n\t\tthis.property = property;\n\t\tthis.value = value;\n\t}\n\n\tpublic override toJSON(): UnknownEnumKeyErrorJsonified {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tmessage: this.message,\n\t\t\tproperty: this.property,\n\t\t\tvalue: this.value\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst property = options.stylize(this.property.toString(), 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[UnknownPropertyError: ${property}]`, 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1, compact: true };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\t\tconst given = inspect(this.value, newOptions).replace(/\\n/g, padding);\n\n\t\tconst header = `${options.stylize('UnknownPropertyError', 'special')} > ${property}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst givenBlock = `\\n  ${options.stylize('Received:', 'regexp')}${padding}${given}`;\n\t\treturn `${header}\\n  ${message}\\n${givenBlock}`;\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport type { ValidatorError } from './BaseValidator';\nimport { BaseValidator } from './imports';\nimport { getValue } from './util/getValue';\n\nexport class DefaultValidator<T> extends BaseValidator<T> {\n\tprivate readonly validator: BaseValidator<T>;\n\tprivate defaultValue: T | (() => T);\n\n\tpublic constructor(\n\t\tvalidator: BaseValidator<T>,\n\t\tvalue: T | (() => T),\n\t\tvalidatorOptions: ValidatorOptions = {},\n\t\tconstraints: readonly IConstraint<T>[] = []\n\t) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.validator = validator;\n\t\tthis.defaultValue = value;\n\t}\n\n\tpublic override default(\n\t\tvalue: Exclude<T, undefined> | (() => Exclude<T, undefined>),\n\t\toptions = this.validatorOptions\n\t): DefaultValidator<Exclude<T, undefined>> {\n\t\tconst clone = this.clone() as unknown as DefaultValidator<Exclude<T, undefined>>;\n\t\tclone.validatorOptions = options;\n\t\tclone.defaultValue = value;\n\t\treturn clone;\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidatorError> {\n\t\treturn typeof value === 'undefined' //\n\t\t\t? Result.ok(getValue(this.defaultValue))\n\t\t\t: this.validator['handle'](value); // eslint-disable-line @typescript-eslint/dot-notation\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validator, this.defaultValue, this.validatorOptions, this.constraints]);\n\t}\n}\n", "import type { InspectOptionsStylized } from 'util';\nimport type { ValidatorOptions } from '../util-types';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\n\nexport class CombinedError extends BaseError {\n\tpublic readonly errors: readonly BaseError[];\n\n\tpublic constructor(errors: readonly BaseError[], validatorOptions?: ValidatorOptions) {\n\t\tsuper(validatorOptions?.message ?? 'Received one or more errors');\n\n\t\tthis.errors = errors;\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize('[CombinedError]', 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1, compact: true };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\n\t\tconst header = `${options.stylize('CombinedError', 'special')} (${options.stylize(this.errors.length.toString(), 'number')})`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst errors = this.errors\n\t\t\t.map((error, i) => {\n\t\t\t\tconst index = options.stylize((i + 1).toString(), 'number');\n\t\t\t\tconst body = error[customInspectSymbolStackLess](depth - 1, newOptions).replace(/\\n/g, padding);\n\n\t\t\t\treturn `  ${index} ${body}`;\n\t\t\t})\n\t\t\t.join('\\n\\n');\n\t\treturn `${header}\\n  ${message}\\n\\n${errors}`;\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedError } from '../lib/errors/CombinedError';\nimport type { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator, LiteralValidator, NullishValidator } from './imports';\n\nexport class UnionValidator<T> extends BaseValidator<T> {\n\tprivate validators: readonly BaseValidator<T>[];\n\n\tpublic constructor(validators: readonly BaseValidator<T>[], validatorOptions?: ValidatorOptions, constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.validators = validators;\n\t}\n\n\tpublic override optional(options: ValidatorOptions = this.validatorOptions): UnionValidator<T | undefined> {\n\t\tif (this.validators.length === 0)\n\t\t\treturn new UnionValidator<T | undefined>([new LiteralValidator(undefined, options)], this.validatorOptions, this.constraints);\n\n\t\tconst [validator] = this.validators;\n\t\tif (validator instanceof LiteralValidator) {\n\t\t\t// If already optional, return a clone:\n\t\t\tif (validator.expected === undefined) return this.clone();\n\n\t\t\t// If it's nullable, convert the nullable validator into a nullish validator to optimize `null | undefined`:\n\t\t\tif (validator.expected === null) {\n\t\t\t\treturn new UnionValidator<T | null | undefined>(\n\t\t\t\t\t[new NullishValidator(options), ...this.validators.slice(1)],\n\t\t\t\t\tthis.validatorOptions,\n\t\t\t\t\tthis.constraints\n\t\t\t\t) as UnionValidator<T | undefined>;\n\t\t\t}\n\t\t} else if (validator instanceof NullishValidator) {\n\t\t\t// If it's already nullish (which validates optional), return a clone:\n\t\t\treturn this.clone();\n\t\t}\n\n\t\treturn new UnionValidator([new LiteralValidator(undefined, options), ...this.validators], this.validatorOptions);\n\t}\n\n\tpublic required(options: ValidatorOptions = this.validatorOptions): UnionValidator<Exclude<T, undefined>> {\n\t\ttype RequiredValidator = UnionValidator<Exclude<T, undefined>>;\n\n\t\tif (this.validators.length === 0) return this.clone() as unknown as RequiredValidator;\n\n\t\tconst [validator] = this.validators;\n\t\tif (validator instanceof LiteralValidator) {\n\t\t\tif (validator.expected === undefined) {\n\t\t\t\treturn new UnionValidator(this.validators.slice(1), this.validatorOptions, this.constraints) as RequiredValidator;\n\t\t\t}\n\t\t} else if (validator instanceof NullishValidator) {\n\t\t\treturn new UnionValidator(\n\t\t\t\t[new LiteralValidator(null, options), ...this.validators.slice(1)],\n\t\t\t\tthis.validatorOptions,\n\t\t\t\tthis.constraints\n\t\t\t) as RequiredValidator;\n\t\t}\n\n\t\treturn this.clone() as unknown as RequiredValidator;\n\t}\n\n\tpublic override nullable(options: ValidatorOptions = this.validatorOptions): UnionValidator<T | null> {\n\t\tif (this.validators.length === 0) {\n\t\t\treturn new UnionValidator<T | null>([new LiteralValidator(null, options)], this.validatorOptions, this.constraints);\n\t\t}\n\n\t\tconst [validator] = this.validators;\n\t\tif (validator instanceof LiteralValidator) {\n\t\t\t// If already nullable, return a clone:\n\t\t\tif (validator.expected === null) return this.clone();\n\n\t\t\t// If it's optional, convert the optional validator into a nullish validator to optimize `null | undefined`:\n\t\t\tif (validator.expected === undefined) {\n\t\t\t\treturn new UnionValidator<T | null | undefined>(\n\t\t\t\t\t[new NullishValidator(options), ...this.validators.slice(1)],\n\t\t\t\t\tthis.validatorOptions,\n\t\t\t\t\tthis.constraints\n\t\t\t\t) as UnionValidator<T | null>;\n\t\t\t}\n\t\t} else if (validator instanceof NullishValidator) {\n\t\t\t// If it's already nullish (which validates nullable), return a clone:\n\t\t\treturn this.clone();\n\t\t}\n\n\t\treturn new UnionValidator([new LiteralValidator(null, options), ...this.validators], this.validatorOptions);\n\t}\n\n\tpublic override nullish(options: ValidatorOptions = this.validatorOptions): UnionValidator<T | null | undefined> {\n\t\tif (this.validators.length === 0) {\n\t\t\treturn new UnionValidator<T | null | undefined>([new NullishValidator(options)], options, this.constraints);\n\t\t}\n\n\t\tconst [validator] = this.validators;\n\t\tif (validator instanceof LiteralValidator) {\n\t\t\t// If already nullable or optional, promote the union to nullish:\n\t\t\tif (validator.expected === null || validator.expected === undefined) {\n\t\t\t\treturn new UnionValidator<T | null | undefined>(\n\t\t\t\t\t[new NullishValidator(options), ...this.validators.slice(1)],\n\t\t\t\t\toptions,\n\t\t\t\t\tthis.constraints\n\t\t\t\t);\n\t\t\t}\n\t\t} else if (validator instanceof NullishValidator) {\n\t\t\t// If it's already nullish, return a clone:\n\t\t\treturn this.clone();\n\t\t}\n\n\t\treturn new UnionValidator<T | null | undefined>([new NullishValidator(options), ...this.validators], options);\n\t}\n\n\tpublic override or<O>(...predicates: readonly BaseValidator<O>[]): UnionValidator<T | O> {\n\t\treturn new UnionValidator<T | O>([...this.validators, ...predicates], this.validatorOptions);\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validators, this.validatorOptions, this.constraints]);\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError | CombinedError> {\n\t\tconst errors: BaseError[] = [];\n\n\t\tfor (const validator of this.validators) {\n\t\t\tconst result = validator.run(value);\n\t\t\tif (result.isOk()) return result as Result<T, CombinedError>;\n\t\t\terrors.push(result.error!);\n\t\t}\n\n\t\treturn Result.err(new CombinedError(errors, this.validatorOptions));\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport { MissingPropertyError } from '../lib/errors/MissingPropertyError';\nimport { UnknownPropertyError } from '../lib/errors/UnknownPropertyError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { MappedObjectValidator, UndefinedToOptional, ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './BaseValidator';\nimport { DefaultValidator } from './DefaultValidator';\nimport { LiteralValidator } from './LiteralValidator';\nimport { NullishValidator } from './NullishValidator';\nimport { UnionValidator } from './UnionValidator';\n\nexport class ObjectValidator<T extends object, I = UndefinedToOptional<T>> extends BaseValidator<I> {\n\tpublic readonly shape: MappedObjectValidator<T>;\n\tpublic readonly strategy: ObjectValidatorStrategy;\n\tprivate readonly keys: readonly (keyof I)[] = [];\n\tprivate readonly handleStrategy: (value: object) => Result<I, CombinedPropertyError>;\n\n\tprivate readonly requiredKeys = new Map<keyof I, BaseValidator<unknown>>();\n\tprivate readonly possiblyUndefinedKeys = new Map<keyof I, BaseValidator<unknown>>();\n\tprivate readonly possiblyUndefinedKeysWithDefaults = new Map<keyof I, DefaultValidator<unknown>>();\n\n\tpublic constructor(\n\t\tshape: MappedObjectValidator<T>,\n\t\tstrategy: ObjectValidatorStrategy = ObjectValidatorStrategy.Ignore,\n\t\tvalidatorOptions: ValidatorOptions = {},\n\t\tconstraints: readonly IConstraint<I>[] = []\n\t) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.shape = shape;\n\t\tthis.strategy = strategy;\n\n\t\tswitch (this.strategy) {\n\t\t\tcase ObjectValidatorStrategy.Ignore:\n\t\t\t\tthis.handleStrategy = (value) => this.handleIgnoreStrategy(value);\n\t\t\t\tbreak;\n\t\t\tcase ObjectValidatorStrategy.Strict: {\n\t\t\t\tthis.handleStrategy = (value) => this.handleStrictStrategy(value);\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tcase ObjectValidatorStrategy.Passthrough:\n\t\t\t\tthis.handleStrategy = (value) => this.handlePassthroughStrategy(value);\n\t\t\t\tbreak;\n\t\t}\n\n\t\tconst shapeEntries = Object.entries(shape) as [keyof I, BaseValidator<T>][];\n\t\tthis.keys = shapeEntries.map(([key]) => key);\n\n\t\tfor (const [key, validator] of shapeEntries) {\n\t\t\tif (validator instanceof UnionValidator) {\n\t\t\t\t// eslint-disable-next-line @typescript-eslint/dot-notation\n\t\t\t\tconst [possiblyLiteralOrNullishPredicate] = validator['validators'];\n\n\t\t\t\tif (possiblyLiteralOrNullishPredicate instanceof NullishValidator) {\n\t\t\t\t\tthis.possiblyUndefinedKeys.set(key, validator);\n\t\t\t\t} else if (possiblyLiteralOrNullishPredicate instanceof LiteralValidator) {\n\t\t\t\t\tif (possiblyLiteralOrNullishPredicate.expected === undefined) {\n\t\t\t\t\t\tthis.possiblyUndefinedKeys.set(key, validator);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.requiredKeys.set(key, validator);\n\t\t\t\t\t}\n\t\t\t\t} else if (validator instanceof DefaultValidator) {\n\t\t\t\t\tthis.possiblyUndefinedKeysWithDefaults.set(key, validator);\n\t\t\t\t} else {\n\t\t\t\t\tthis.requiredKeys.set(key, validator);\n\t\t\t\t}\n\t\t\t} else if (validator instanceof NullishValidator) {\n\t\t\t\tthis.possiblyUndefinedKeys.set(key, validator);\n\t\t\t} else if (validator instanceof LiteralValidator) {\n\t\t\t\tif (validator.expected === undefined) {\n\t\t\t\t\tthis.possiblyUndefinedKeys.set(key, validator);\n\t\t\t\t} else {\n\t\t\t\t\tthis.requiredKeys.set(key, validator);\n\t\t\t\t}\n\t\t\t} else if (validator instanceof DefaultValidator) {\n\t\t\t\tthis.possiblyUndefinedKeysWithDefaults.set(key, validator);\n\t\t\t} else {\n\t\t\t\tthis.requiredKeys.set(key, validator);\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic strict(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn Reflect.construct(this.constructor, [this.shape, ObjectValidatorStrategy.Strict, options, this.constraints]);\n\t}\n\n\tpublic ignore(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn Reflect.construct(this.constructor, [this.shape, ObjectValidatorStrategy.Ignore, options, this.constraints]);\n\t}\n\n\tpublic passthrough(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn Reflect.construct(this.constructor, [this.shape, ObjectValidatorStrategy.Passthrough, options, this.constraints]);\n\t}\n\n\tpublic partial(options: ValidatorOptions = this.validatorOptions): ObjectValidator<{ [Key in keyof I]?: I[Key] }> {\n\t\tconst shape = Object.fromEntries(this.keys.map((key) => [key, this.shape[key as unknown as keyof typeof this.shape].optional(options)]));\n\t\treturn Reflect.construct(this.constructor, [shape, this.strategy, options, this.constraints]);\n\t}\n\n\tpublic required(options: ValidatorOptions = this.validatorOptions): ObjectValidator<{ [Key in keyof I]-?: I[Key] }> {\n\t\tconst shape = Object.fromEntries(\n\t\t\tthis.keys.map((key) => {\n\t\t\t\tlet validator = this.shape[key as unknown as keyof typeof this.shape];\n\t\t\t\tif (validator instanceof UnionValidator) validator = validator.required(options);\n\t\t\t\treturn [key, validator];\n\t\t\t})\n\t\t);\n\t\treturn Reflect.construct(this.constructor, [shape, this.strategy, options, this.constraints]);\n\t}\n\n\tpublic extend<ET extends object>(\n\t\tschema: ObjectValidator<ET> | MappedObjectValidator<ET>,\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): ObjectValidator<T & ET> {\n\t\tconst shape = { ...this.shape, ...(schema instanceof ObjectValidator ? schema.shape : schema) };\n\t\treturn Reflect.construct(this.constructor, [shape, this.strategy, options, this.constraints]);\n\t}\n\n\tpublic pick<K extends keyof I>(\n\t\tkeys: readonly K[],\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): ObjectValidator<{ [Key in keyof Pick<I, K>]: I[Key] }> {\n\t\tconst shape = Object.fromEntries(\n\t\t\tkeys.filter((key) => this.keys.includes(key)).map((key) => [key, this.shape[key as unknown as keyof typeof this.shape]])\n\t\t);\n\t\treturn Reflect.construct(this.constructor, [shape, this.strategy, options, this.constraints]);\n\t}\n\n\tpublic omit<K extends keyof I>(\n\t\tkeys: readonly K[],\n\t\toptions: ValidatorOptions = this.validatorOptions\n\t): ObjectValidator<{ [Key in keyof Omit<I, K>]: I[Key] }> {\n\t\tconst shape = Object.fromEntries(\n\t\t\tthis.keys.filter((key) => !keys.includes(key as any)).map((key) => [key, this.shape[key as unknown as keyof typeof this.shape]])\n\t\t);\n\t\treturn Reflect.construct(this.constructor, [shape, this.strategy, options, this.constraints]);\n\t}\n\n\tprotected override handle(value: unknown): Result<I, ValidationError | CombinedPropertyError> {\n\t\tconst typeOfValue = typeof value;\n\t\tif (typeOfValue !== 'object') {\n\t\t\treturn Result.err(\n\t\t\t\tnew ValidationError(\n\t\t\t\t\t's.object(T)',\n\t\t\t\t\tthis.validatorOptions.message ?? `Expected the value to be an object, but received ${typeOfValue} instead`,\n\t\t\t\t\tvalue\n\t\t\t\t)\n\t\t\t);\n\t\t}\n\n\t\tif (value === null) {\n\t\t\treturn Result.err(new ValidationError('s.object(T)', this.validatorOptions.message ?? 'Expected the value to not be null', value));\n\t\t}\n\n\t\tif (Array.isArray(value)) {\n\t\t\treturn Result.err(new ValidationError('s.object(T)', this.validatorOptions.message ?? 'Expected the value to not be an array', value));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(value as I);\n\t\t}\n\n\t\tfor (const predicate of Object.values(this.shape) as BaseValidator<any>[]) {\n\t\t\tpredicate.setParent(this.parent ?? value!);\n\t\t}\n\n\t\treturn this.handleStrategy(value as object);\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.shape, this.strategy, this.validatorOptions, this.constraints]);\n\t}\n\n\tprivate handleIgnoreStrategy(value: object): Result<I, CombinedPropertyError> {\n\t\tconst errors: [PropertyKey, BaseError][] = [];\n\t\tconst finalObject = {} as I;\n\t\tconst inputEntries = new Map(Object.entries(value) as [keyof I, unknown][]);\n\n\t\tconst runPredicate = (key: keyof I, predicate: BaseValidator<unknown>) => {\n\t\t\tconst result = predicate.run(value[key as keyof object]);\n\n\t\t\tif (result.isOk()) {\n\t\t\t\tfinalObject[key] = result.value as I[keyof I];\n\t\t\t} else {\n\t\t\t\tconst error = result.error!;\n\t\t\t\terrors.push([key, error]);\n\t\t\t}\n\t\t};\n\n\t\tfor (const [key, predicate] of this.requiredKeys) {\n\t\t\tif (inputEntries.delete(key)) {\n\t\t\t\trunPredicate(key, predicate);\n\t\t\t} else {\n\t\t\t\terrors.push([key, new MissingPropertyError(key, this.validatorOptions)]);\n\t\t\t}\n\t\t}\n\n\t\t// Run possibly undefined keys that also have defaults even if there are no more keys to process (this is necessary so we fill in those defaults)\n\t\tfor (const [key, validator] of this.possiblyUndefinedKeysWithDefaults) {\n\t\t\tinputEntries.delete(key);\n\t\t\trunPredicate(key, validator);\n\t\t}\n\n\t\t// Early exit if there are no more properties to validate in the object and there are errors to report\n\t\tif (inputEntries.size === 0) {\n\t\t\treturn errors.length === 0 //\n\t\t\t\t? Result.ok(finalObject)\n\t\t\t\t: Result.err(new CombinedPropertyError(errors, this.validatorOptions));\n\t\t}\n\n\t\t// In the event the remaining keys to check are less than the number of possible undefined keys, we check those\n\t\t// as it could yield a faster execution\n\t\tconst checkInputEntriesInsteadOfSchemaKeys = this.possiblyUndefinedKeys.size > inputEntries.size;\n\n\t\tif (checkInputEntriesInsteadOfSchemaKeys) {\n\t\t\tfor (const [key] of inputEntries) {\n\t\t\t\tconst predicate = this.possiblyUndefinedKeys.get(key);\n\n\t\t\t\tif (predicate) {\n\t\t\t\t\trunPredicate(key, predicate);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tfor (const [key, predicate] of this.possiblyUndefinedKeys) {\n\t\t\t\tif (inputEntries.delete(key)) {\n\t\t\t\t\trunPredicate(key, predicate);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(finalObject)\n\t\t\t: Result.err(new CombinedPropertyError(errors, this.validatorOptions));\n\t}\n\n\tprivate handleStrictStrategy(value: object): Result<I, CombinedPropertyError> {\n\t\tconst errors: [PropertyKey, BaseError][] = [];\n\t\tconst finalResult = {} as I;\n\t\tconst inputEntries = new Map(Object.entries(value) as [keyof I, unknown][]);\n\n\t\tconst runPredicate = (key: keyof I, predicate: BaseValidator<unknown>) => {\n\t\t\tconst result = predicate.run(value[key as keyof object]);\n\n\t\t\tif (result.isOk()) {\n\t\t\t\tfinalResult[key] = result.value as I[keyof I];\n\t\t\t} else {\n\t\t\t\tconst error = result.error!;\n\t\t\t\terrors.push([key, error]);\n\t\t\t}\n\t\t};\n\n\t\tfor (const [key, predicate] of this.requiredKeys) {\n\t\t\tif (inputEntries.delete(key)) {\n\t\t\t\trunPredicate(key, predicate);\n\t\t\t} else {\n\t\t\t\terrors.push([key, new MissingPropertyError(key, this.validatorOptions)]);\n\t\t\t}\n\t\t}\n\n\t\t// Run possibly undefined keys that also have defaults even if there are no more keys to process (this is necessary so we fill in those defaults)\n\t\tfor (const [key, validator] of this.possiblyUndefinedKeysWithDefaults) {\n\t\t\tinputEntries.delete(key);\n\t\t\trunPredicate(key, validator);\n\t\t}\n\n\t\tfor (const [key, predicate] of this.possiblyUndefinedKeys) {\n\t\t\t// All of these validators are assumed to be possibly undefined, so if we have gone through the entire object and there's still validators,\n\t\t\t// safe to assume we're done here\n\t\t\tif (inputEntries.size === 0) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tif (inputEntries.delete(key)) {\n\t\t\t\trunPredicate(key, predicate);\n\t\t\t}\n\t\t}\n\n\t\tif (inputEntries.size !== 0) {\n\t\t\tfor (const [key, value] of inputEntries.entries()) {\n\t\t\t\terrors.push([key, new UnknownPropertyError(key, value, this.validatorOptions)]);\n\t\t\t}\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(finalResult)\n\t\t\t: Result.err(new CombinedPropertyError(errors, this.validatorOptions));\n\t}\n\n\tprivate handlePassthroughStrategy(value: object): Result<I, CombinedPropertyError> {\n\t\tconst result = this.handleIgnoreStrategy(value);\n\t\treturn result.isErr() ? result : Result.ok({ ...value, ...result.value } as I);\n\t}\n}\n\nexport enum ObjectValidatorStrategy {\n\tIgnore,\n\tStrict,\n\tPassthrough\n}\n", "import type { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class PassthroughValidator<T extends any | unknown> extends BaseValidator<T> {\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn Result.ok(value as T);\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class RecordValidator<T> extends BaseValidator<Record<string, T>> {\n\tprivate readonly validator: BaseValidator<T>;\n\n\tpublic constructor(\n\t\tvalidator: BaseValidator<T>,\n\t\tvalidatorOptions: ValidatorOptions = {},\n\t\tconstraints: readonly IConstraint<Record<string, T>>[] = []\n\t) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.validator = validator;\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validator, this.validatorOptions, this.constraints]);\n\t}\n\n\tprotected handle(value: unknown): Result<Record<string, T>, ValidationError | CombinedPropertyError> {\n\t\tif (typeof value !== 'object') {\n\t\t\treturn Result.err(new ValidationError('s.record(T)', this.validatorOptions.message ?? 'Expected an object', value));\n\t\t}\n\n\t\tif (value === null) {\n\t\t\treturn Result.err(new ValidationError('s.record(T)', this.validatorOptions.message ?? 'Expected the value to not be null', value));\n\t\t}\n\n\t\tif (Array.isArray(value)) {\n\t\t\treturn Result.err(new ValidationError('s.record(T)', this.validatorOptions.message ?? 'Expected the value to not be an array', value));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(value as Record<string, T>);\n\t\t}\n\n\t\tconst errors: [string, BaseError][] = [];\n\t\tconst transformed: Record<string, T> = {};\n\n\t\tfor (const [key, val] of Object.entries(value!)) {\n\t\t\tconst result = this.validator.run(val);\n\t\t\tif (result.isOk()) transformed[key] = result.value;\n\t\t\telse errors.push([key, result.error!]);\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(transformed)\n\t\t\t: Result.err(new CombinedPropertyError(errors, this.validatorOptions));\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedError } from '../lib/errors/CombinedError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class SetValidator<T> extends BaseValidator<Set<T>> {\n\tprivate readonly validator: BaseValidator<T>;\n\n\tpublic constructor(validator: BaseValidator<T>, validatorOptions?: ValidatorOptions, constraints: readonly IConstraint<Set<T>>[] = []) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.validator = validator;\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validator, this.validatorOptions, this.constraints]);\n\t}\n\n\tprotected handle(values: unknown): Result<Set<T>, ValidationError | CombinedError> {\n\t\tif (!(values instanceof Set)) {\n\t\t\treturn Result.err(new ValidationError('s.set(T)', this.validatorOptions.message ?? 'Expected a set', values));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(values);\n\t\t}\n\n\t\tconst errors: BaseError[] = [];\n\t\tconst transformed = new Set<T>();\n\n\t\tfor (const value of values) {\n\t\t\tconst result = this.validator.run(value);\n\t\t\tif (result.isOk()) transformed.add(result.value);\n\t\t\telse errors.push(result.error!);\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(transformed)\n\t\t\t: Result.err(new CombinedError(errors, this.validatorOptions));\n\t}\n}\n", "/**\n * @license MIT\n * @copyright 2020 <PERSON>\n * @see https://github.com/colinhacks/zod/blob/master/LICENSE\n */\nconst accountRegex = /^(?!\\.)(?!.*\\.\\.)([A-Z0-9_+-\\.]*)[A-Z0-9_+-]$/i;\n\n/**\n * Validates an email address string based on various checks:\n * - It must be a non nullish and non empty string\n * - It must include at least an `@` symbol`\n * - The account name may not exceed 64 characters\n * - The domain name may not exceed 255 characters\n * - The domain must include at least one `.` symbol\n * - Each part of the domain, split by `.` must not exceed 63 characters\n * - The email address must be [RFC-5322](https://datatracker.ietf.org/doc/html/rfc5322) compliant\n * @param email The email to validate\n * @returns `true` if the email is valid, `false` otherwise\n *\n * @remark Based on the following sources:\n * - `email-validator` by [manish<PERSON><PERSON>](https://github.com/manishsaraan) ([code](https://github.com/manishsaraan/email-validator/blob/master/index.js))\n * - [Comparing E-mail Address Validating Regular Expressions](http://fightingforalostcause.net/misc/2006/compare-email-regex.php)\n * - [Validating Email Addresses by Derrick Pallas](http://thedailywtf.com/Articles/Validating_Email_Addresses.aspx)\n * - [StackOverflow answer by bortzmeyer](http://stackoverflow.com/questions/201323/what-is-the-best-regular-expression-for-validating-email-addresses/201378#201378)\n * - [The wikipedia page on Email addresses](https://en.wikipedia.org/wiki/Email_address)\n */\nexport function validateEmail(email: string): boolean {\n\t// 1. Non-nullish and non-empty string check.\n\t//\n\t// If a nullish or empty email was provided then do an early exit\n\tif (!email) return false;\n\n\t// Find the location of the @ symbol:\n\tconst atIndex = email.indexOf('@');\n\n\t// 2. @ presence check.\n\t//\n\t// If the email does not have the @ symbol, it's automatically invalid:\n\tif (atIndex === -1) return false;\n\n\t// 3. <account> maximum length check.\n\t//\n\t// From <account>@<domain>, if <account> exceeds 64 characters, then the\n\t// position of the @ symbol is 64 or greater. In this case, the email is\n\t// invalid:\n\tif (atIndex > 64) return false;\n\n\tconst domainIndex = atIndex + 1;\n\n\t// 7.1. Duplicated @ symbol check.\n\t//\n\t// If there's a second @ symbol, the email is automatically invalid:\n\tif (email.includes('@', domainIndex)) return false;\n\n\t// 4. <domain> maximum length check.\n\t//\n\t// From <account>@<domain>, if <domain> exceeds 255 characters, then it\n\t// means that the amount of characters between the start of <domain> and the\n\t// end of the string is separated by 255 or more characters.\n\tif (email.length - domainIndex > 255) return false;\n\n\t// Find the location of the . symbol in <domain>:\n\tlet dotIndex = email.indexOf('.', domainIndex);\n\n\t// 5. <domain> dot (.) symbol check.\n\t//\n\t// From <account>@<domain>, if <domain> does not contain a dot (.) symbol,\n\t// then it means the domain is invalid.\n\tif (dotIndex === -1) return false;\n\n\t// 6. <domain> parts length.\n\t//\n\t// Assign a temporary variable to store the start of the last read domain\n\t// part, this would be at the start of <domain>.\n\t//\n\t// For a <domain> part to be correct, it must have at most, 63 characters.\n\t// We repeat this step for every sub-section of <domain> contained within\n\t// dot (.) symbols.\n\t//\n\t// The following step is a more optimized version of the following code:\n\t//\n\t// ```javascript\n\t// domain.split('.').some((part) => part.length > 63);\n\t// ```\n\tlet lastDotIndex = domainIndex;\n\tdo {\n\t\tif (dotIndex - lastDotIndex > 63) return false;\n\n\t\tlastDotIndex = dotIndex + 1;\n\t} while ((dotIndex = email.indexOf('.', lastDotIndex)) !== -1);\n\n\t// The loop iterates from the first to the n - 1 part, this line checks for\n\t// the last (n) part:\n\tif (email.length - lastDotIndex > 63) return false;\n\n\t// 7.2. Character checks.\n\t//\n\t// From <account>@<domain>:\n\t// - Extract the <account> part by slicing the input from start to the @\n\t//   character. Validate afterwards.\n\t// - Extract the <domain> part by slicing the input from the start of\n\t//   <domain>. Validate afterwards.\n\t//\n\t// Note: we inline the variables so <domain> isn't created unless the\n\t//       <account> check passes.\n\treturn accountRegex.test(email.slice(0, atIndex)) && validateEmailDomain(email.slice(domainIndex));\n}\n\nfunction validateEmailDomain(domain: string): boolean {\n\ttry {\n\t\treturn new URL(`http://${domain}`).hostname === domain;\n\t} catch {\n\t\treturn false;\n\t}\n}\n", "/**\n * Code ported from https://github.com/nodejs/node/blob/5fad0b93667ffc6e4def52996b9529ac99b26319/lib/internal/net.js\n */\n\n// IPv4 Segment\nconst v4Seg = '(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])';\nconst v4Str = `(${v4Seg}[.]){3}${v4Seg}`;\nconst IPv4Reg = new RegExp(`^${v4Str}$`);\n\n// IPv6 Segment\nconst v6Seg = '(?:[0-9a-fA-F]{1,4})';\nconst IPv6Reg = new RegExp(\n\t'^(' +\n\t\t`(?:${v6Seg}:){7}(?:${v6Seg}|:)|` +\n\t\t`(?:${v6Seg}:){6}(?:${v4Str}|:${v6Seg}|:)|` +\n\t\t`(?:${v6Seg}:){5}(?::${v4Str}|(:${v6Seg}){1,2}|:)|` +\n\t\t`(?:${v6Seg}:){4}(?:(:${v6Seg}){0,1}:${v4Str}|(:${v6Seg}){1,3}|:)|` +\n\t\t`(?:${v6Seg}:){3}(?:(:${v6Seg}){0,2}:${v4Str}|(:${v6Seg}){1,4}|:)|` +\n\t\t`(?:${v6Seg}:){2}(?:(:${v6Seg}){0,3}:${v4Str}|(:${v6Seg}){1,5}|:)|` +\n\t\t`(?:${v6Seg}:){1}(?:(:${v6Seg}){0,4}:${v4Str}|(:${v6Seg}){1,6}|:)|` +\n\t\t`(?::((?::${v6Seg}){0,5}:${v4Str}|(?::${v6Seg}){1,7}|:))` +\n\t\t')(%[0-9a-zA-Z-.:]{1,})?$'\n);\n\nexport function isIPv4(s: string): boolean {\n\treturn IPv4Reg.test(s);\n}\n\nexport function isIPv6(s: string): boolean {\n\treturn IPv6Reg.test(s);\n}\n\nexport function isIP(s: string): number {\n\tif (isIPv4(s)) return 4;\n\tif (isIPv6(s)) return 6;\n\treturn 0;\n}\n", "export const phoneNumberRegex = /^((?:\\+|0{0,2})\\d{1,2}\\s?)?\\(?\\d{3}\\)?[\\s.-]?\\d{3}[\\s.-]?\\d{4}$/;\n\nexport function validatePhoneNumber(input: string) {\n\treturn phoneNumberRegex.test(input);\n}\n", "import { inspect, type InspectOptionsStylized } from 'util';\nimport { BaseConstraintError, type ConstraintErrorNames } from './BaseConstraintError';\nimport { customInspectSymbolStackLess } from './BaseError';\nimport type { MultiplePossibilitiesConstraintErrorJsonified } from './error-types';\n\nexport class MultiplePossibilitiesConstraintError<T = unknown> extends BaseConstraintError<T> {\n\tpublic readonly expected: readonly string[];\n\n\tpublic constructor(constraint: ConstraintErrorNames, message: string, given: T, expected: readonly string[]) {\n\t\tsuper(constraint, message, given);\n\t\tthis.expected = expected;\n\t}\n\n\tpublic override toJSON(): MultiplePossibilitiesConstraintErrorJsonified<T> {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tmessage: this.message,\n\t\t\tconstraint: this.constraint,\n\t\t\tgiven: this.given,\n\t\t\texpected: this.expected\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst constraint = options.stylize(this.constraint, 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[MultiplePossibilitiesConstraintError: ${constraint}]`, 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1 };\n\n\t\tconst verticalLine = options.stylize('|', 'undefined');\n\t\tconst padding = `\\n  ${verticalLine} `;\n\t\tconst given = inspect(this.given, newOptions).replace(/\\n/g, padding);\n\n\t\tconst header = `${options.stylize('MultiplePossibilitiesConstraintError', 'special')} > ${constraint}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\n\t\tconst expectedPadding = `\\n  ${verticalLine} - `;\n\t\tconst expectedBlock = `\\n  ${options.stylize('Expected any of the following:', 'string')}${expectedPadding}${this.expected\n\t\t\t.map((possible) => options.stylize(possible, 'boolean'))\n\t\t\t.join(expectedPadding)}`;\n\t\tconst givenBlock = `\\n  ${options.stylize('Received:', 'regexp')}${padding}${given}`;\n\t\treturn `${header}\\n  ${message}\\n${expectedBlock}\\n${givenBlock}`;\n\t}\n}\n", "export function combinedErrorFn<P extends [...any], E extends Error>(...fns: ErrorFn<P, E>[]): ErrorFn<P, E> {\n\tswitch (fns.length) {\n\t\tcase 0:\n\t\t\treturn () => null;\n\t\tcase 1:\n\t\t\treturn fns[0];\n\t\tcase 2: {\n\t\t\tconst [fn0, fn1] = fns;\n\t\t\treturn (...params) => fn0(...params) || fn1(...params);\n\t\t}\n\t\tdefault: {\n\t\t\treturn (...params) => {\n\t\t\t\tfor (const fn of fns) {\n\t\t\t\t\tconst result = fn(...params);\n\t\t\t\t\tif (result) return result;\n\t\t\t\t}\n\n\t\t\t\treturn null;\n\t\t\t};\n\t\t}\n\t}\n}\n\nexport type ErrorFn<P extends [...any], E extends Error> = (...params: P) => E | null;\n", "import { MultiplePossibilitiesConstraintError } from '../../lib/errors/MultiplePossibilitiesConstraintError';\nimport type { ValidatorOptions } from '../../lib/util-types';\nimport { combinedErrorFn, type ErrorFn } from './common/combinedResultFn';\n\nexport type StringProtocol = `${string}:`;\n\nexport type StringDomain = `${string}.${string}`;\n\nexport interface UrlOptions {\n\tallowedProtocols?: StringProtocol[];\n\tallowedDomains?: StringDomain[];\n}\n\nexport function createUrlValidators(options?: UrlOptions, validatorOptions?: ValidatorOptions) {\n\tconst fns: ErrorFn<[input: string, url: URL], MultiplePossibilitiesConstraintError<string>>[] = [];\n\n\tif (options?.allowedProtocols?.length) fns.push(allowedProtocolsFn(options.allowedProtocols, validatorOptions));\n\tif (options?.allowedDomains?.length) fns.push(allowedDomainsFn(options.allowedDomains, validatorOptions));\n\n\treturn combinedErrorFn(...fns);\n}\n\nfunction allowedProtocolsFn(allowedProtocols: StringProtocol[], options?: ValidatorOptions) {\n\treturn (input: string, url: URL) =>\n\t\tallowedProtocols.includes(url.protocol as StringProtocol)\n\t\t\t? null\n\t\t\t: new MultiplePossibilitiesConstraintError('s.string().url()', options?.message ?? 'Invalid URL protocol', input, allowedProtocols);\n}\n\nfunction allowedDomainsFn(allowedDomains: StringDomain[], options?: ValidatorOptions) {\n\treturn (input: string, url: URL) =>\n\t\tallowedDomains.includes(url.hostname as StringDomain)\n\t\t\t? null\n\t\t\t: new MultiplePossibilitiesConstraintError('s.string().url()', options?.message ?? 'Invalid URL domain', input, allowedDomains);\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport type { IConstraint } from './base/IConstraint';\nimport { validateEmail } from './util/emailValidator';\nimport { isIP, isIPv4, isIPv6 } from './util/net';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\nimport { validatePhoneNumber } from './util/phoneValidator';\nimport { createUrlValidators } from './util/urlValidators';\n\nexport type StringConstraintName = `s.string().${\n\t| `length${'LessThan' | 'LessThanOrEqual' | 'GreaterThan' | 'GreaterThanOrEqual' | 'Equal' | 'NotEqual'}`\n\t| 'regex'\n\t| 'url'\n\t| 'uuid'\n\t| 'email'\n\t| `ip${'v4' | 'v6' | ''}`\n\t| 'date'\n\t| 'phone'}()`;\n\nexport type StringProtocol = `${string}:`;\n\nexport type StringDomain = `${string}.${string}`;\n\nexport interface UrlOptions {\n\tallowedProtocols?: StringProtocol[];\n\tallowedDomains?: StringDomain[];\n}\n\nexport type UUIDVersion = 1 | 3 | 4 | 5;\n\nexport interface StringUuidOptions {\n\tversion?: UUIDVersion | `${UUIDVersion}-${UUIDVersion}` | null;\n\tnullable?: boolean;\n}\n\nfunction stringLengthComparator(\n\tcomparator: Comparator,\n\tname: StringConstraintName,\n\texpected: string,\n\tlength: number,\n\toptions?: ValidatorOptions\n): IConstraint<string> {\n\treturn {\n\t\trun(input: string) {\n\t\t\treturn comparator(input.length, length) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, options?.message ?? 'Invalid string length', input, expected));\n\t\t}\n\t};\n}\n\nexport function stringLengthLessThan(length: number, options?: ValidatorOptions): IConstraint<string> {\n\tconst expected = `expected.length < ${length}`;\n\treturn stringLengthComparator(lessThan, 's.string().lengthLessThan()', expected, length, options);\n}\n\nexport function stringLengthLessThanOrEqual(length: number, options?: ValidatorOptions): IConstraint<string> {\n\tconst expected = `expected.length <= ${length}`;\n\treturn stringLengthComparator(lessThanOrEqual, 's.string().lengthLessThanOrEqual()', expected, length, options);\n}\n\nexport function stringLengthGreaterThan(length: number, options?: ValidatorOptions): IConstraint<string> {\n\tconst expected = `expected.length > ${length}`;\n\treturn stringLengthComparator(greaterThan, 's.string().lengthGreaterThan()', expected, length, options);\n}\n\nexport function stringLengthGreaterThanOrEqual(length: number, options?: ValidatorOptions): IConstraint<string> {\n\tconst expected = `expected.length >= ${length}`;\n\treturn stringLengthComparator(greaterThanOrEqual, 's.string().lengthGreaterThanOrEqual()', expected, length, options);\n}\n\nexport function stringLengthEqual(length: number, options?: ValidatorOptions): IConstraint<string> {\n\tconst expected = `expected.length === ${length}`;\n\treturn stringLengthComparator(equal, 's.string().lengthEqual()', expected, length, options);\n}\n\nexport function stringLengthNotEqual(length: number, options?: ValidatorOptions): IConstraint<string> {\n\tconst expected = `expected.length !== ${length}`;\n\treturn stringLengthComparator(notEqual, 's.string().lengthNotEqual()', expected, length, options);\n}\n\nexport function stringEmail(options?: ValidatorOptions): IConstraint<string> {\n\treturn {\n\t\trun(input: string) {\n\t\t\treturn validateEmail(input)\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.string().email()',\n\t\t\t\t\t\t\toptions?.message ?? 'Invalid email address',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\t'expected to be an email address'\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nfunction stringRegexValidator(type: StringConstraintName, expected: string, regex: RegExp, options?: ValidatorOptions): IConstraint<string> {\n\treturn {\n\t\trun(input: string) {\n\t\t\treturn regex.test(input) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(type, options?.message ?? 'Invalid string format', input, expected));\n\t\t}\n\t};\n}\n\nexport function stringUrl(options?: UrlOptions, validatorOptions?: ValidatorOptions): IConstraint<string> {\n\tconst validatorFn = createUrlValidators(options, validatorOptions);\n\treturn {\n\t\trun(input: string) {\n\t\t\tlet url: URL;\n\t\t\ttry {\n\t\t\t\turl = new URL(input);\n\t\t\t} catch {\n\t\t\t\treturn Result.err(\n\t\t\t\t\tnew ExpectedConstraintError('s.string().url()', validatorOptions?.message ?? 'Invalid URL', input, 'expected to match a URL')\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tconst validatorFnResult = validatorFn(input, url);\n\t\t\tif (validatorFnResult === null) return Result.ok(input);\n\t\t\treturn Result.err(validatorFnResult);\n\t\t}\n\t};\n}\n\nexport function stringIp(version?: 4 | 6, options?: ValidatorOptions): IConstraint<string> {\n\tconst ipVersion = version ? (`v${version}` as const) : '';\n\tconst validatorFn = version === 4 ? isIPv4 : version === 6 ? isIPv6 : isIP;\n\n\tconst name = `s.string().ip${ipVersion}()` as const;\n\tconst message = `Invalid IP${ipVersion} address`;\n\tconst expected = `expected to be an IP${ipVersion} address`;\n\treturn {\n\t\trun(input: string) {\n\t\t\treturn validatorFn(input)\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, options?.message ?? message, input, expected));\n\t\t}\n\t};\n}\n\nexport function stringRegex(regex: RegExp, options?: ValidatorOptions) {\n\treturn stringRegexValidator('s.string().regex()', `expected ${regex}.test(expected) to be true`, regex, options);\n}\n\nexport function stringUuid({ version = 4, nullable = false }: StringUuidOptions = {}, options?: ValidatorOptions) {\n\tversion ??= '1-5';\n\tconst regex = new RegExp(\n\t\t`^(?:[0-9A-F]{8}-[0-9A-F]{4}-[${version}][0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}${\n\t\t\tnullable ? '|00000000-0000-0000-0000-000000000000' : ''\n\t\t})$`,\n\t\t'i'\n\t);\n\tconst expected = `expected to match UUID${typeof version === 'number' ? `v${version}` : ` in range of ${version}`}`;\n\treturn stringRegexValidator('s.string().uuid()', expected, regex, options);\n}\n\nexport function stringDate(options?: ValidatorOptions): IConstraint<string> {\n\treturn {\n\t\trun(input: string) {\n\t\t\tconst time = Date.parse(input);\n\n\t\t\treturn Number.isNaN(time)\n\t\t\t\t? Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.string().date()',\n\t\t\t\t\t\t\toptions?.message ?? 'Invalid date string',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\t'expected to be a valid date string (in the ISO 8601 or ECMA-262 format)'\n\t\t\t\t\t\t)\n\t\t\t\t\t)\n\t\t\t\t: Result.ok(input);\n\t\t}\n\t};\n}\n\nexport function stringPhone(options?: ValidatorOptions): IConstraint<string> {\n\treturn {\n\t\trun(input: string) {\n\t\t\treturn validatePhoneNumber(input)\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.string().phone()',\n\t\t\t\t\t\t\toptions?.message ?? 'Invalid phone number',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\t'expected to be a phone number'\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport {\n\tstringDate,\n\tstringEmail,\n\tstringIp,\n\tstringLengthEqual,\n\tstringLengthGreaterThan,\n\tstringLengthGreaterThanOrEqual,\n\tstringLengthLessThan,\n\tstringLengthLessThanOrEqual,\n\tstringLengthNotEqual,\n\tstringPhone,\n\tstringRegex,\n\tstringUrl,\n\tstringUuid,\n\ttype StringUuidOptions,\n\ttype UrlOptions\n} from '../constraints/StringConstraints';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class StringValidator<T extends string> extends BaseValidator<T> {\n\tpublic lengthLessThan(length: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(stringLengthLessThan(length, options) as IConstraint<T>);\n\t}\n\n\tpublic lengthLessThanOrEqual(length: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(stringLengthLessThanOrEqual(length, options) as IConstraint<T>);\n\t}\n\n\tpublic lengthGreaterThan(length: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(stringLengthGreaterThan(length, options) as IConstraint<T>);\n\t}\n\n\tpublic lengthGreaterThanOrEqual(length: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(stringLengthGreaterThanOrEqual(length, options) as IConstraint<T>);\n\t}\n\n\tpublic lengthEqual(length: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(stringLengthEqual(length, options) as IConstraint<T>);\n\t}\n\n\tpublic lengthNotEqual(length: number, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(stringLengthNotEqual(length, options) as IConstraint<T>);\n\t}\n\n\tpublic email(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(stringEmail(options) as IConstraint<T>);\n\t}\n\n\tpublic url(validatorOptions?: ValidatorOptions): this;\n\tpublic url(options?: UrlOptions, validatorOptions?: ValidatorOptions): this;\n\tpublic url(options?: UrlOptions | ValidatorOptions, validatorOptions: ValidatorOptions = this.validatorOptions): this {\n\t\tconst urlOptions = this.isUrlOptions(options);\n\n\t\tif (urlOptions) {\n\t\t\treturn this.addConstraint(stringUrl(options, validatorOptions) as IConstraint<T>);\n\t\t}\n\n\t\treturn this.addConstraint(stringUrl(undefined, validatorOptions) as IConstraint<T>);\n\t}\n\n\tpublic uuid(validatorOptions?: ValidatorOptions): this;\n\tpublic uuid(options?: StringUuidOptions, validatorOptions?: ValidatorOptions): this;\n\tpublic uuid(options?: StringUuidOptions | ValidatorOptions, validatorOptions: ValidatorOptions = this.validatorOptions): this {\n\t\tconst stringUuidOptions = this.isStringUuidOptions(options);\n\n\t\tif (stringUuidOptions) {\n\t\t\treturn this.addConstraint(stringUuid(options, validatorOptions) as IConstraint<T>);\n\t\t}\n\n\t\treturn this.addConstraint(stringUuid(undefined, validatorOptions) as IConstraint<T>);\n\t}\n\n\tpublic regex(regex: RegExp, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(stringRegex(regex, options) as IConstraint<T>);\n\t}\n\n\tpublic date(options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(stringDate(options) as IConstraint<T>);\n\t}\n\n\tpublic ipv4(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.ip(4, options);\n\t}\n\n\tpublic ipv6(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.ip(6, options);\n\t}\n\n\tpublic ip(version?: 4 | 6, options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(stringIp(version, options) as IConstraint<T>);\n\t}\n\n\tpublic phone(options: ValidatorOptions = this.validatorOptions): this {\n\t\treturn this.addConstraint(stringPhone(options) as IConstraint<T>);\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn typeof value === 'string' //\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ValidationError('s.string()', this.validatorOptions.message ?? 'Expected a string primitive', value));\n\t}\n\n\tprivate isUrlOptions(options?: UrlOptions | ValidatorOptions): options is UrlOptions {\n\t\treturn (options as ValidatorOptions)?.message === undefined;\n\t}\n\n\tprivate isStringUuidOptions(options?: StringUuidOptions | ValidatorOptions): options is StringUuidOptions {\n\t\treturn (options as ValidatorOptions)?.message === undefined;\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class TupleValidator<T extends any[]> extends BaseValidator<[...T]> {\n\tprivate readonly validators: BaseValidator<[...T]>[] = [];\n\n\tpublic constructor(\n\t\tvalidators: BaseValidator<[...T]>[],\n\t\tvalidatorOptions: ValidatorOptions = {},\n\t\tconstraints: readonly IConstraint<[...T]>[] = []\n\t) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.validators = validators;\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validators, this.validatorOptions, this.constraints]);\n\t}\n\n\tprotected handle(values: unknown): Result<[...T], ValidationError | CombinedPropertyError> {\n\t\tif (!Array.isArray(values)) {\n\t\t\treturn Result.err(new ValidationError('s.tuple(T)', this.validatorOptions.message ?? 'Expected an array', values));\n\t\t}\n\n\t\tif (values.length !== this.validators.length) {\n\t\t\treturn Result.err(\n\t\t\t\tnew ValidationError('s.tuple(T)', this.validatorOptions.message ?? `Expected an array of length ${this.validators.length}`, values)\n\t\t\t);\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(values as [...T]);\n\t\t}\n\n\t\tconst errors: [number, BaseError][] = [];\n\t\tconst transformed: T = [] as unknown as T;\n\n\t\tfor (let i = 0; i < values.length; i++) {\n\t\t\tconst result = this.validators[i].run(values[i]);\n\t\t\tif (result.isOk()) transformed.push(result.value);\n\t\t\telse errors.push([i, result.error!]);\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(transformed)\n\t\t\t: Result.err(new CombinedPropertyError(errors, this.validatorOptions));\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class MapValidator<K, V> extends BaseValidator<Map<K, V>> {\n\tprivate readonly keyValidator: BaseValidator<K>;\n\tprivate readonly valueValidator: BaseValidator<V>;\n\n\tpublic constructor(\n\t\tkeyValidator: BaseValidator<K>,\n\t\tvalueValidator: BaseValidator<V>,\n\t\tvalidatorOptions: ValidatorOptions = {},\n\t\tconstraints: readonly IConstraint<Map<K, V>>[] = []\n\t) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.keyValidator = keyValidator;\n\t\tthis.valueValidator = valueValidator;\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.keyValidator, this.valueValidator, this.validatorOptions, this.constraints]);\n\t}\n\n\tprotected handle(value: unknown): Result<Map<K, V>, ValidationError | CombinedPropertyError> {\n\t\tif (!(value instanceof Map)) {\n\t\t\treturn Result.err(new ValidationError('s.map(K, V)', this.validatorOptions.message ?? 'Expected a map', value));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(value);\n\t\t}\n\n\t\tconst errors: [string, BaseError][] = [];\n\t\tconst transformed = new Map<K, V>();\n\n\t\tfor (const [key, val] of value.entries()) {\n\t\t\tconst keyResult = this.keyValidator.run(key);\n\t\t\tconst valueResult = this.valueValidator.run(val);\n\t\t\tconst { length } = errors;\n\t\t\tif (keyResult.isErr()) errors.push([key, keyResult.error]);\n\t\t\tif (valueResult.isErr()) errors.push([key, valueResult.error]);\n\t\t\tif (errors.length === length) transformed.set(keyResult.value!, valueResult.value!);\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(transformed)\n\t\t\t: Result.err(new CombinedPropertyError(errors, this.validatorOptions));\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { Result } from '../lib/Result';\nimport type { Unwrap, ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator, type ValidatorError } from './imports';\n\nexport class LazyValidator<T extends BaseValidator<unknown>, R = Unwrap<T>> extends BaseValidator<R> {\n\tprivate readonly validator: (value: unknown) => T;\n\n\tpublic constructor(validator: (value: unknown) => T, validatorOptions: ValidatorOptions = {}, constraints: readonly IConstraint<R>[] = []) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.validator = validator;\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validator, this.validatorOptions, this.constraints]);\n\t}\n\n\tprotected handle(values: unknown): Result<R, ValidatorError> {\n\t\treturn this.validator(values).run(values) as Result<R, ValidatorError>;\n\t}\n}\n", "import type { InspectOptionsStylized } from 'util';\nimport type { ValidatorOptions } from '../util-types';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\nimport type { UnknownEnumValueErrorJsonified } from './error-types';\n\nexport class UnknownEnumValueError extends BaseError {\n\tpublic readonly value: string | number;\n\tpublic readonly enumKeys: string[];\n\tpublic readonly enumMappings: Map<string | number, string | number>;\n\n\tpublic constructor(\n\t\tvalue: string | number,\n\t\tkeys: string[],\n\t\tenumMappings: Map<string | number, string | number>,\n\t\tvalidatorOptions?: ValidatorOptions\n\t) {\n\t\tsuper(validatorOptions?.message ?? 'Expected the value to be one of the following enum values:');\n\n\t\tthis.value = value;\n\t\tthis.enumKeys = keys;\n\t\tthis.enumMappings = enumMappings;\n\t}\n\n\tpublic override toJSON(): UnknownEnumValueErrorJsonified {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tmessage: this.message,\n\t\t\tvalue: this.value,\n\t\t\tenumKeys: this.enumKeys,\n\t\t\tenumMappings: [...this.enumMappings.entries()]\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst value = options.stylize(this.value.toString(), 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[UnknownEnumValueError: ${value}]`, 'special');\n\t\t}\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\t\tconst pairs = this.enumKeys\n\t\t\t.map((key) => {\n\t\t\t\tconst enumValue = this.enumMappings.get(key)!;\n\t\t\t\treturn `${options.stylize(key, 'string')} or ${options.stylize(\n\t\t\t\t\tenumValue.toString(),\n\t\t\t\t\ttypeof enumValue === 'number' ? 'number' : 'string'\n\t\t\t\t)}`;\n\t\t\t})\n\t\t\t.join(padding);\n\n\t\tconst header = `${options.stylize('UnknownEnumValueError', 'special')} > ${value}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst pairsBlock = `${padding}${pairs}`;\n\t\treturn `${header}\\n  ${message}\\n${pairsBlock}`;\n\t}\n}\n", "import { UnknownEnumValueError } from '../lib/errors/UnknownEnumValueError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class NativeEnumValidator<T extends NativeEnumLike> extends BaseValidator<T[keyof T]> {\n\tpublic readonly enumShape: T;\n\tpublic readonly hasNumericElements: boolean = false;\n\tprivate readonly enumKeys: string[];\n\tprivate readonly enumMapping = new Map<string | number, T[keyof T]>();\n\n\tpublic constructor(enumShape: T, validatorOptions: ValidatorOptions = {}) {\n\t\tsuper(validatorOptions);\n\t\tthis.enumShape = enumShape;\n\n\t\tthis.enumKeys = Object.keys(enumShape).filter((key) => {\n\t\t\treturn typeof enumShape[enumShape[key]] !== 'number';\n\t\t});\n\n\t\tfor (const key of this.enumKeys) {\n\t\t\tconst enumValue = enumShape[key] as T[keyof T];\n\n\t\t\tthis.enumMapping.set(key, enumValue);\n\t\t\tthis.enumMapping.set(enumValue, enumValue);\n\n\t\t\tif (typeof enumValue === 'number') {\n\t\t\t\tthis.hasNumericElements = true;\n\t\t\t\tthis.enumMapping.set(`${enumValue}`, enumValue);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected override handle(value: unknown): Result<T[keyof T], ValidationError | UnknownEnumValueError> {\n\t\tconst typeOfValue = typeof value;\n\n\t\tif (typeOfValue === 'number') {\n\t\t\tif (!this.hasNumericElements) {\n\t\t\t\treturn Result.err(\n\t\t\t\t\tnew ValidationError('s.nativeEnum(T)', this.validatorOptions.message ?? 'Expected the value to be a string', value)\n\t\t\t\t);\n\t\t\t}\n\t\t} else if (typeOfValue !== 'string') {\n\t\t\t// typeOfValue !== 'number' is implied here\n\t\t\treturn Result.err(\n\t\t\t\tnew ValidationError('s.nativeEnum(T)', this.validatorOptions.message ?? 'Expected the value to be a string or number', value)\n\t\t\t);\n\t\t}\n\n\t\tconst casted = value as string | number;\n\n\t\tconst possibleEnumValue = this.enumMapping.get(casted);\n\n\t\treturn typeof possibleEnumValue === 'undefined'\n\t\t\t? Result.err(new UnknownEnumValueError(casted, this.enumKeys, this.enumMapping, this.validatorOptions))\n\t\t\t: Result.ok(possibleEnumValue);\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.enumShape, this.validatorOptions]);\n\t}\n}\n\nexport interface NativeEnumLike {\n\t[key: string]: string | number;\n\t[key: number]: string;\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport type { IConstraint } from './base/IConstraint';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\nimport type { TypedArray } from './util/typedArray';\n\nexport type TypedArrayConstraintName = `s.typedArray(T).${'byteLength' | 'length'}${\n\t| 'LessThan'\n\t| 'LessThanOrEqual'\n\t| 'GreaterThan'\n\t| 'GreaterThanOrEqual'\n\t| 'Equal'\n\t| 'NotEqual'\n\t| 'Range'\n\t| 'RangeInclusive'\n\t| 'RangeExclusive'}()`;\n\nfunction typedArrayByteLengthComparator<T extends TypedArray>(\n\tcomparator: Comparator,\n\tname: TypedArrayConstraintName,\n\texpected: string,\n\tlength: number,\n\toptions?: ValidatorOptions\n): IConstraint<T> {\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn comparator(input.byteLength, length) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, options?.message ?? 'Invalid Typed Array byte length', input, expected));\n\t\t}\n\t};\n}\n\nexport function typedArrayByteLengthLessThan<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.byteLength < ${value}`;\n\treturn typedArrayByteLengthComparator(lessThan, 's.typedArray(T).byteLengthLessThan()', expected, value, options);\n}\n\nexport function typedArrayByteLengthLessThanOrEqual<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.byteLength <= ${value}`;\n\treturn typedArrayByteLengthComparator(lessThanOrEqual, 's.typedArray(T).byteLengthLessThanOrEqual()', expected, value, options);\n}\n\nexport function typedArrayByteLengthGreaterThan<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.byteLength > ${value}`;\n\treturn typedArrayByteLengthComparator(greaterThan, 's.typedArray(T).byteLengthGreaterThan()', expected, value, options);\n}\n\nexport function typedArrayByteLengthGreaterThanOrEqual<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.byteLength >= ${value}`;\n\treturn typedArrayByteLengthComparator(greaterThanOrEqual, 's.typedArray(T).byteLengthGreaterThanOrEqual()', expected, value, options);\n}\n\nexport function typedArrayByteLengthEqual<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.byteLength === ${value}`;\n\treturn typedArrayByteLengthComparator(equal, 's.typedArray(T).byteLengthEqual()', expected, value, options);\n}\n\nexport function typedArrayByteLengthNotEqual<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.byteLength !== ${value}`;\n\treturn typedArrayByteLengthComparator(notEqual, 's.typedArray(T).byteLengthNotEqual()', expected, value, options);\n}\n\nexport function typedArrayByteLengthRange<T extends TypedArray>(start: number, endBefore: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.byteLength >= ${start} && expected.byteLength < ${endBefore}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.byteLength >= start && input.byteLength < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.typedArray(T).byteLengthRange()',\n\t\t\t\t\t\t\toptions?.message ?? 'Invalid Typed Array byte length',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\texpected\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nexport function typedArrayByteLengthRangeInclusive<T extends TypedArray>(start: number, end: number, options?: ValidatorOptions) {\n\tconst expected = `expected.byteLength >= ${start} && expected.byteLength <= ${end}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.byteLength >= start && input.byteLength <= end //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.typedArray(T).byteLengthRangeInclusive()',\n\t\t\t\t\t\t\toptions?.message ?? 'Invalid Typed Array byte length',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\texpected\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nexport function typedArrayByteLengthRangeExclusive<T extends TypedArray>(\n\tstartAfter: number,\n\tendBefore: number,\n\toptions?: ValidatorOptions\n): IConstraint<T> {\n\tconst expected = `expected.byteLength > ${startAfter} && expected.byteLength < ${endBefore}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.byteLength > startAfter && input.byteLength < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.typedArray(T).byteLengthRangeExclusive()',\n\t\t\t\t\t\t\toptions?.message ?? 'Invalid Typed Array byte length',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\texpected\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nfunction typedArrayLengthComparator<T extends TypedArray>(\n\tcomparator: Comparator,\n\tname: TypedArrayConstraintName,\n\texpected: string,\n\tlength: number,\n\toptions?: ValidatorOptions\n): IConstraint<T> {\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn comparator(input.length, length) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, options?.message ?? 'Invalid Typed Array length', input, expected));\n\t\t}\n\t};\n}\n\nexport function typedArrayLengthLessThan<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.length < ${value}`;\n\treturn typedArrayLengthComparator(lessThan, 's.typedArray(T).lengthLessThan()', expected, value, options);\n}\n\nexport function typedArrayLengthLessThanOrEqual<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.length <= ${value}`;\n\treturn typedArrayLengthComparator(lessThanOrEqual, 's.typedArray(T).lengthLessThanOrEqual()', expected, value, options);\n}\n\nexport function typedArrayLengthGreaterThan<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.length > ${value}`;\n\treturn typedArrayLengthComparator(greaterThan, 's.typedArray(T).lengthGreaterThan()', expected, value, options);\n}\n\nexport function typedArrayLengthGreaterThanOrEqual<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.length >= ${value}`;\n\treturn typedArrayLengthComparator(greaterThanOrEqual, 's.typedArray(T).lengthGreaterThanOrEqual()', expected, value, options);\n}\n\nexport function typedArrayLengthEqual<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.length === ${value}`;\n\treturn typedArrayLengthComparator(equal, 's.typedArray(T).lengthEqual()', expected, value, options);\n}\n\nexport function typedArrayLengthNotEqual<T extends TypedArray>(value: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.length !== ${value}`;\n\treturn typedArrayLengthComparator(notEqual, 's.typedArray(T).lengthNotEqual()', expected, value, options);\n}\n\nexport function typedArrayLengthRange<T extends TypedArray>(start: number, endBefore: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.length >= ${start} && expected.length < ${endBefore}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.length >= start && input.length < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.typedArray(T).lengthRange()',\n\t\t\t\t\t\t\toptions?.message ?? 'Invalid Typed Array length',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\texpected\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nexport function typedArrayLengthRangeInclusive<T extends TypedArray>(start: number, end: number, options?: ValidatorOptions): IConstraint<T> {\n\tconst expected = `expected.length >= ${start} && expected.length <= ${end}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.length >= start && input.length <= end //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.typedArray(T).lengthRangeInclusive()',\n\t\t\t\t\t\t\toptions?.message ?? 'Invalid Typed Array length',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\texpected\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n\nexport function typedArrayLengthRangeExclusive<T extends TypedArray>(\n\tstartAfter: number,\n\tendBefore: number,\n\toptions?: ValidatorOptions\n): IConstraint<T> {\n\tconst expected = `expected.length > ${startAfter} && expected.length < ${endBefore}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.length > startAfter && input.length < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.typedArray(T).lengthRangeExclusive()',\n\t\t\t\t\t\t\toptions?.message ?? 'Invalid Typed Array length',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\texpected\n\t\t\t\t\t\t)\n\t\t\t\t\t);\n\t\t}\n\t};\n}\n", "const vowels = ['a', 'e', 'i', 'o', 'u'];\n\nexport const aOrAn = (word: string) => {\n\treturn `${vowels.includes(word[0].toLowerCase()) ? 'an' : 'a'} ${word}`;\n};\n", "export type TypedArray =\n\t| Int8Array\n\t| Uint8Array\n\t| Uint8ClampedArray\n\t| Int16Array\n\t| Uint16Array\n\t| Int32Array\n\t| Uint32Array\n\t| Float32Array\n\t| Float64Array\n\t| BigInt64Array\n\t| BigUint64Array;\n\nexport const TypedArrays = {\n\tInt8Array: (x: unknown): x is Int8Array => x instanceof Int8Array,\n\tUint8Array: (x: unknown): x is Uint8Array => x instanceof Uint8Array,\n\tUint8ClampedArray: (x: unknown): x is Uint8ClampedArray => x instanceof Uint8ClampedArray,\n\tInt16Array: (x: unknown): x is Int16Array => x instanceof Int16Array,\n\tUint16Array: (x: unknown): x is Uint16Array => x instanceof Uint16Array,\n\tInt32Array: (x: unknown): x is Int32Array => x instanceof Int32Array,\n\tUint32Array: (x: unknown): x is Uint32Array => x instanceof Uint32Array,\n\tFloat32Array: (x: unknown): x is Float32Array => x instanceof Float32Array,\n\tFloat64Array: (x: unknown): x is Float64Array => x instanceof Float64Array,\n\tBigInt64Array: (x: unknown): x is BigInt64Array => x instanceof BigInt64Array,\n\tBigUint64Array: (x: unknown): x is BigUint64Array => x instanceof BigUint64Array,\n\tTypedArray: (x: unknown): x is TypedArray => ArrayBuffer.isView(x) && !(x instanceof DataView)\n} as const;\n\nexport type TypedArrayName = keyof typeof TypedArrays;\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport {\n\ttypedArrayByteLengthEqual,\n\ttypedArrayByteLengthGreater<PERSON>han,\n\ttypedArrayByteLengthGreaterThanOrEqual,\n\ttypedArrayByteLengthLessThan,\n\ttypedArrayByteLengthLessThanOrEqual,\n\ttypedArrayByteLengthNotEqual,\n\ttypedArrayByteLengthRange,\n\ttypedArrayByteLengthRangeExclusive,\n\ttypedArrayByteLengthRangeInclusive,\n\ttypedArrayLengthEqual,\n\ttypedArrayLengthGreaterThan,\n\ttypedArrayLengthGreaterThanOrEqual,\n\ttypedArrayLengthLessThan,\n\ttypedArrayLengthLessThanOrEqual,\n\ttypedArrayLengthNotEqual,\n\ttypedArrayLengthRange,\n\ttypedArrayLengthRangeExclusive,\n\ttypedArrayLengthRangeInclusive\n} from '../constraints/TypedArrayLengthConstraints';\nimport { aOrAn } from '../constraints/util/common/vowels';\nimport { TypedArrays, type TypedArray, type TypedArrayName } from '../constraints/util/typedArray';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ValidatorOptions } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class TypedArrayValidator<T extends TypedArray> extends BaseValidator<T> {\n\tprivate readonly type: TypedArrayName;\n\n\tpublic constructor(type: TypedArrayName, validatorOptions: ValidatorOptions = {}, constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(validatorOptions, constraints);\n\t\tthis.type = type;\n\t}\n\n\tpublic byteLengthLessThan(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayByteLengthLessThan(length, options));\n\t}\n\n\tpublic byteLengthLessThanOrEqual(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayByteLengthLessThanOrEqual(length, options));\n\t}\n\n\tpublic byteLengthGreaterThan(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayByteLengthGreaterThan(length, options));\n\t}\n\n\tpublic byteLengthGreaterThanOrEqual(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayByteLengthGreaterThanOrEqual(length, options));\n\t}\n\n\tpublic byteLengthEqual(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayByteLengthEqual(length, options));\n\t}\n\n\tpublic byteLengthNotEqual(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayByteLengthNotEqual(length, options));\n\t}\n\n\tpublic byteLengthRange(start: number, endBefore: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayByteLengthRange(start, endBefore, options));\n\t}\n\n\tpublic byteLengthRangeInclusive(startAt: number, endAt: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayByteLengthRangeInclusive(startAt, endAt, options) as IConstraint<T>);\n\t}\n\n\tpublic byteLengthRangeExclusive(startAfter: number, endBefore: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayByteLengthRangeExclusive(startAfter, endBefore, options));\n\t}\n\n\tpublic lengthLessThan(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayLengthLessThan(length, options));\n\t}\n\n\tpublic lengthLessThanOrEqual(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayLengthLessThanOrEqual(length, options));\n\t}\n\n\tpublic lengthGreaterThan(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayLengthGreaterThan(length, options));\n\t}\n\n\tpublic lengthGreaterThanOrEqual(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayLengthGreaterThanOrEqual(length, options));\n\t}\n\n\tpublic lengthEqual(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayLengthEqual(length, options));\n\t}\n\n\tpublic lengthNotEqual(length: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayLengthNotEqual(length, options));\n\t}\n\n\tpublic lengthRange(start: number, endBefore: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayLengthRange(start, endBefore, options));\n\t}\n\n\tpublic lengthRangeInclusive(startAt: number, endAt: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayLengthRangeInclusive(startAt, endAt, options));\n\t}\n\n\tpublic lengthRangeExclusive(startAfter: number, endBefore: number, options: ValidatorOptions = this.validatorOptions) {\n\t\treturn this.addConstraint(typedArrayLengthRangeExclusive(startAfter, endBefore, options));\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.type, this.validatorOptions, this.constraints]);\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn TypedArrays[this.type](value)\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ValidationError('s.typedArray()', this.validatorOptions.message ?? `Expected ${aOrAn(this.type)}`, value));\n\t}\n}\n", "import type { TypedArray, TypedArrayName } from '../constraints/util/typedArray';\nimport type { Unwrap, UnwrapTuple, ValidatorOptions } from '../lib/util-types';\nimport {\n\tArrayValidator,\n\tBaseValidator,\n\tBigIntValidator,\n\tBooleanValidator,\n\tDateValidator,\n\tInstanceValidator,\n\tLiteralValidator,\n\tMapValidator,\n\tNeverValidator,\n\tNullishValidator,\n\tNumberValidator,\n\tObjectValidator,\n\tObjectValidatorStrategy,\n\tPassthroughValidator,\n\tRecordValidator,\n\tSetValidator,\n\tStringValidator,\n\tTupleValidator,\n\tUnionValidator\n} from '../validators/imports';\nimport { LazyValidator } from '../validators/LazyValidator';\nimport { NativeEnumValidator, type NativeEnumLike } from '../validators/NativeEnumValidator';\nimport { TypedArrayValidator } from '../validators/TypedArrayValidator';\nimport type { Constructor, MappedObjectValidator } from './util-types';\n\nexport class Shapes {\n\tpublic string(options?: ValidatorOptions) {\n\t\treturn new StringValidator(options);\n\t}\n\n\tpublic number(options?: ValidatorOptions) {\n\t\treturn new NumberValidator(options);\n\t}\n\n\tpublic bigint(options?: ValidatorOptions) {\n\t\treturn new BigIntValidator(options);\n\t}\n\n\tpublic boolean(options?: ValidatorOptions) {\n\t\treturn new BooleanValidator(options);\n\t}\n\n\tpublic date(options?: ValidatorOptions) {\n\t\treturn new DateValidator(options);\n\t}\n\n\tpublic object<T extends object>(shape: MappedObjectValidator<T>, options?: ValidatorOptions) {\n\t\treturn new ObjectValidator<T>(shape, ObjectValidatorStrategy.Ignore, options);\n\t}\n\n\tpublic undefined(options?: ValidatorOptions) {\n\t\treturn this.literal(undefined, { equalsOptions: options });\n\t}\n\n\tpublic null(options?: ValidatorOptions) {\n\t\treturn this.literal(null, { equalsOptions: options });\n\t}\n\n\tpublic nullish(options?: ValidatorOptions) {\n\t\treturn new NullishValidator(options);\n\t}\n\n\tpublic any(options?: ValidatorOptions) {\n\t\treturn new PassthroughValidator<any>(options);\n\t}\n\n\tpublic unknown(options?: ValidatorOptions) {\n\t\treturn new PassthroughValidator<unknown>(options);\n\t}\n\n\tpublic never(options?: ValidatorOptions) {\n\t\treturn new NeverValidator(options);\n\t}\n\n\tpublic enum<T>(values: readonly T[], options?: ValidatorOptions) {\n\t\treturn this.union(\n\t\t\tvalues.map((value) => this.literal(value, { equalsOptions: options })),\n\t\t\toptions\n\t\t);\n\t}\n\n\tpublic nativeEnum<T extends NativeEnumLike>(enumShape: T, options?: ValidatorOptions): NativeEnumValidator<T> {\n\t\treturn new NativeEnumValidator(enumShape, options);\n\t}\n\n\tpublic literal<T>(value: T, options?: { dateOptions?: ValidatorOptions; equalsOptions?: ValidatorOptions }): BaseValidator<T> {\n\t\tif (value instanceof Date) {\n\t\t\treturn this.date(options?.dateOptions).equal(value, options?.equalsOptions) as unknown as BaseValidator<T>;\n\t\t}\n\n\t\treturn new LiteralValidator(value, options?.equalsOptions);\n\t}\n\n\tpublic instance<T>(expected: Constructor<T>, options?: ValidatorOptions): InstanceValidator<T> {\n\t\treturn new InstanceValidator(expected, options);\n\t}\n\n\tpublic union<T extends BaseValidator<any>[]>(validators: T, options?: ValidatorOptions): UnionValidator<Unwrap<T[number]>> {\n\t\treturn new UnionValidator(validators, options);\n\t}\n\n\tpublic array<T>(validator: BaseValidator<T[][number]>, options?: ValidatorOptions): ArrayValidator<T[], T[][number]>;\n\tpublic array<T extends unknown[]>(validator: BaseValidator<T[number]>, options?: ValidatorOptions): ArrayValidator<T, T[number]>;\n\tpublic array<T extends unknown[]>(validator: BaseValidator<T[number]>, options?: ValidatorOptions) {\n\t\treturn new ArrayValidator(validator, options);\n\t}\n\n\tpublic typedArray<T extends TypedArray>(type: TypedArrayName = 'TypedArray', options?: ValidatorOptions) {\n\t\treturn new TypedArrayValidator<T>(type, options);\n\t}\n\n\tpublic int8Array(options?: ValidatorOptions) {\n\t\treturn this.typedArray<Int8Array>('Int8Array', options);\n\t}\n\n\tpublic uint8Array(options?: ValidatorOptions) {\n\t\treturn this.typedArray<Uint8Array>('Uint8Array', options);\n\t}\n\n\tpublic uint8ClampedArray(options?: ValidatorOptions) {\n\t\treturn this.typedArray<Uint8ClampedArray>('Uint8ClampedArray', options);\n\t}\n\n\tpublic int16Array(options?: ValidatorOptions) {\n\t\treturn this.typedArray<Int16Array>('Int16Array', options);\n\t}\n\n\tpublic uint16Array(options?: ValidatorOptions) {\n\t\treturn this.typedArray<Uint16Array>('Uint16Array', options);\n\t}\n\n\tpublic int32Array(options?: ValidatorOptions) {\n\t\treturn this.typedArray<Int32Array>('Int32Array', options);\n\t}\n\n\tpublic uint32Array(options?: ValidatorOptions) {\n\t\treturn this.typedArray<Uint32Array>('Uint32Array', options);\n\t}\n\n\tpublic float32Array(options?: ValidatorOptions) {\n\t\treturn this.typedArray<Float32Array>('Float32Array', options);\n\t}\n\n\tpublic float64Array(options?: ValidatorOptions) {\n\t\treturn this.typedArray<Float64Array>('Float64Array', options);\n\t}\n\n\tpublic bigInt64Array(options?: ValidatorOptions) {\n\t\treturn this.typedArray<BigInt64Array>('BigInt64Array', options);\n\t}\n\n\tpublic bigUint64Array(options?: ValidatorOptions) {\n\t\treturn this.typedArray<BigUint64Array>('BigUint64Array', options);\n\t}\n\n\tpublic tuple<T extends [...BaseValidator<any>[]]>(validators: [...T], options?: ValidatorOptions): TupleValidator<UnwrapTuple<T>> {\n\t\treturn new TupleValidator(validators, options);\n\t}\n\n\tpublic set<T>(validator: BaseValidator<T>, options?: ValidatorOptions) {\n\t\treturn new SetValidator(validator, options);\n\t}\n\n\tpublic record<T>(validator: BaseValidator<T>, options?: ValidatorOptions) {\n\t\treturn new RecordValidator(validator, options);\n\t}\n\n\tpublic map<T, U>(keyValidator: BaseValidator<T>, valueValidator: BaseValidator<U>, options?: ValidatorOptions) {\n\t\treturn new MapValidator(keyValidator, valueValidator, options);\n\t}\n\n\tpublic lazy<T extends BaseValidator<unknown>>(validator: (value: unknown) => T, options?: ValidatorOptions) {\n\t\treturn new LazyValidator(validator, options);\n\t}\n}\n", "import { Shapes } from './lib/Shapes';\n\nexport const s = new Shapes();\n\nexport * from './lib/Result';\nexport * from './lib/configs';\nexport * from './lib/errors/BaseError';\nexport * from './lib/errors/CombinedError';\nexport * from './lib/errors/CombinedPropertyError';\nexport * from './lib/errors/ExpectedConstraintError';\nexport * from './lib/errors/ExpectedValidationError';\nexport * from './lib/errors/MissingPropertyError';\nexport * from './lib/errors/MultiplePossibilitiesConstraintError';\nexport * from './lib/errors/UnknownEnumValueError';\nexport * from './lib/errors/UnknownPropertyError';\nexport * from './lib/errors/ValidationError';\nexport * from './type-exports';\n"]}