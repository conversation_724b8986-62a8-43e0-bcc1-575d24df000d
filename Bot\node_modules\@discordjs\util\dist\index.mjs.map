{"version": 3, "sources": ["../src/functions/lazy.ts", "../src/functions/range.ts", "../src/functions/calculateShardId.ts", "../src/functions/runtime.ts", "../src/functions/userAgentAppendix.ts", "../src/functions/polyfillDispose.ts", "../src/JSONEncodable.ts", "../src/Equatable.ts", "../src/index.ts"], "sourcesContent": ["/**\n * Lazy is a wrapper around a value that is computed lazily. It is useful for\n * cases where the value is expensive to compute and the computation may not\n * be needed at all.\n *\n * @param cb - The callback to lazily evaluate\n * @typeParam Value - The type of the value\n * @example\n * ```ts\n * const value = lazy(() => computeExpensiveValue());\n * ```\n */\n// eslint-disable-next-line promise/prefer-await-to-callbacks\nexport function lazy<Value>(cb: () => Value): () => Value {\n\tlet defaultValue: Value;\n\t// eslint-disable-next-line promise/prefer-await-to-callbacks\n\treturn () => (defaultValue ??= cb());\n}\n", "/**\n * Options for creating a range\n */\nexport interface RangeOptions {\n\t/**\n\t * The end of the range (exclusive)\n\t */\n\tend: number;\n\t/**\n\t * The start of the range (inclusive)\n\t */\n\tstart: number;\n\t/**\n\t * The amount to increment by\n\t *\n\t * @defaultValue `1`\n\t */\n\tstep?: number;\n}\n\n/**\n * A generator to yield numbers in a given range\n *\n * @remarks\n * This method is end-exclusive, for example the last number yielded by `range(5)` is 4. If you\n * prefer for the end to be included add 1 to the range or `end` option.\n * @param range - A number representing the range to yield (exclusive) or an object with start, end and step\n * @example\n * Basic range\n * ```ts\n * for (const number of range(5)) {\n *  console.log(number);\n * }\n * // Prints 0, 1, 2, 3, 4\n * ```\n * @example\n * Range with a step\n * ```ts\n * for (const number of range({ start: 3, end: 10, step: 2 })) {\n * \tconsole.log(number);\n * }\n * // Prints 3, 5, 7, 9\n * ```\n */\nexport function* range(range: RangeOptions | number) {\n\tlet rangeEnd: number;\n\tlet start = 0;\n\tlet step = 1;\n\n\tif (typeof range === 'number') {\n\t\trangeEnd = range;\n\t} else {\n\t\tstart = range.start;\n\t\trangeEnd = range.end;\n\t\tstep = range.step ?? 1;\n\t}\n\n\tfor (let index = start; index < rangeEnd; index += step) {\n\t\tyield index;\n\t}\n}\n", "/**\n * Calculates the shard id for a given guild id.\n *\n * @param guildId - The guild id to calculate the shard id for\n * @param shardCount - The total number of shards\n */\nexport function calculateShardId(guildId: string, shardCount: number) {\n\treturn Number(BigInt(guildId) >> 22n) % shardCount;\n}\n", "/* eslint-disable n/prefer-global/process */\n\nexport function shouldUseGlobalFetchAndWebSocket() {\n\t// Browser env and deno when ran directly\n\tif (typeof globalThis.process === 'undefined') {\n\t\treturn 'fetch' in globalThis && 'WebSocket' in globalThis;\n\t}\n\n\tif ('versions' in globalThis.process) {\n\t\treturn 'deno' in globalThis.process.versions || 'bun' in globalThis.process.versions;\n\t}\n\n\treturn false;\n}\n", "/* eslint-disable n/prefer-global/process */\n\n/**\n * Resolves the user agent appendix string for the current environment.\n */\nexport function getUserAgentAppendix(): string {\n\t// https://vercel.com/docs/concepts/functions/edge-functions/edge-runtime#check-if-you're-running-on-the-edge-runtime\n\t// @ts-expect-error Vercel Edge functions\n\tif (typeof globalThis.EdgeRuntime !== 'undefined') {\n\t\treturn 'Vercel-Edge-Functions';\n\t}\n\n\t// @ts-expect-error Cloudflare Workers\n\tif (typeof globalThis.R2 !== 'undefined' && typeof globalThis.WebSocketPair !== 'undefined') {\n\t\t// https://developers.cloudflare.com/workers/runtime-apis/web-standards/#navigatoruseragent\n\t\treturn 'Cloudflare-Workers';\n\t}\n\n\t// https://docs.netlify.com/edge-functions/api/#netlify-global-object\n\t// @ts-expect-error Netlify Edge functions\n\tif (typeof globalThis.Netlify !== 'undefined') {\n\t\treturn 'Netlify-Edge-Functions';\n\t}\n\n\t// Most (if not all) edge environments will have `process` defined. Within a web browser we'll extract it using `navigator.userAgent`.\n\tif (typeof globalThis.process !== 'object') {\n\t\t// @ts-expect-error web env\n\t\tif (typeof globalThis.navigator === 'object') {\n\t\t\t// @ts-expect-error web env\n\t\t\treturn globalThis.navigator.userAgent;\n\t\t}\n\n\t\treturn 'UnknownEnvironment';\n\t}\n\n\tif ('versions' in globalThis.process) {\n\t\tif ('deno' in globalThis.process.versions) {\n\t\t\treturn `Deno/${globalThis.process.versions.deno}`;\n\t\t}\n\n\t\tif ('bun' in globalThis.process.versions) {\n\t\t\treturn `Bun/${globalThis.process.versions.bun}`;\n\t\t}\n\n\t\tif ('node' in globalThis.process.versions) {\n\t\t\treturn `Node.js/${globalThis.process.versions.node}`;\n\t\t}\n\t}\n\n\treturn 'UnknownEnvironment';\n}\n", "/**\n * Polyfill for `Symbol.dispose` and `Symbol.asyncDispose` which is used as a part of\n * {@link https://github.com/tc39/proposal-explicit-resource-management}. Node versions below 18.x\n * don't have these symbols by default, so we need to polyfill them.\n */\nexport function polyfillDispose() {\n\t// Polyfill for `Symbol.dispose` and `Symbol.asyncDispose` if not available.\n\t// Taken from https://www.typescriptlang.org/docs/handbook/release-notes/typescript-5-2.html#using-declarations-and-explicit-resource-management\n\n\t// @ts-expect-error This is a polyfill, so it's fine to write\n\tSymbol.dispose ??= Symbol('Symbol.dispose');\n\t// @ts-expect-error Same as above\n\tSymbol.asyncDispose ??= Symbol('Symbol.asyncDispose');\n}\n", "/**\n * Represents an object capable of representing itself as a JSON object\n *\n * @typeParam Value - The JSON type corresponding to {@link JSONEncodable.toJSON} outputs.\n */\nexport interface JSONEncodable<Value> {\n\t/**\n\t * Transforms this object to its JSON format\n\t */\n\ttoJSON(): Value;\n}\n\n/**\n * Indicates if an object is encodable or not.\n *\n * @param maybeEncodable - The object to check against\n */\nexport function isJSONEncodable(maybeEncodable: unknown): maybeEncodable is JSONEncodable<unknown> {\n\treturn maybeEncodable !== null && typeof maybeEncodable === 'object' && 'toJSON' in maybeEncodable;\n}\n", "/**\n * Represents a structure that can be checked against another\n * given structure for equality\n *\n * @typeParam Value - The type of object to compare the current object to\n */\nexport interface Equatable<Value> {\n\t/**\n\t * Whether or not this is equal to another structure\n\t */\n\tequals(other: Value): boolean;\n}\n\n/**\n * Indicates if an object is equatable or not.\n *\n * @param maybeEquatable - The object to check against\n */\nexport function isEquatable(maybeEquatable: unknown): maybeEquatable is Equatable<unknown> {\n\treturn maybeEquatable !== null && typeof maybeEquatable === 'object' && 'equals' in maybeEquatable;\n}\n", "export * from './types.js';\nexport * from './functions/index.js';\nexport * from './JSONEncodable.js';\nexport * from './Equatable.js';\n\n/**\n * The {@link https://github.com/discordjs/discord.js/blob/main/packages/util#readme | @discordjs/util} version\n * that you are currently using.\n *\n * @privateRemarks This needs to explicitly be `string` so it is not typed as a \"const string\" that gets injected by esbuild.\n */\nexport const version = '1.1.1' as string;\n"], "mappings": ";;;;AAaO,SAAS,KAAY,IAA8B;AACzD,MAAI;AAEJ,SAAO,MAAO,iBAAiB,GAAG;AACnC;AAJgB;;;AC+BT,UAAU,MAAMA,QAA8B;AACpD,MAAI;AACJ,MAAI,QAAQ;AACZ,MAAI,OAAO;AAEX,MAAI,OAAOA,WAAU,UAAU;AAC9B,eAAWA;AAAA,EACZ,OAAO;AACN,YAAQA,OAAM;AACd,eAAWA,OAAM;AACjB,WAAOA,OAAM,QAAQ;AAAA,EACtB;AAEA,WAAS,QAAQ,OAAO,QAAQ,UAAU,SAAS,MAAM;AACxD,UAAM;AAAA,EACP;AACD;AAhBiB;;;ACtCV,SAAS,iBAAiB,SAAiB,YAAoB;AACrE,SAAO,OAAO,OAAO,OAAO,KAAK,GAAG,IAAI;AACzC;AAFgB;;;ACJT,SAAS,mCAAmC;AAElD,MAAI,OAAO,WAAW,YAAY,aAAa;AAC9C,WAAO,WAAW,cAAc,eAAe;AAAA,EAChD;AAEA,MAAI,cAAc,WAAW,SAAS;AACrC,WAAO,UAAU,WAAW,QAAQ,YAAY,SAAS,WAAW,QAAQ;AAAA,EAC7E;AAEA,SAAO;AACR;AAXgB;;;ACGT,SAAS,uBAA+B;AAG9C,MAAI,OAAO,WAAW,gBAAgB,aAAa;AAClD,WAAO;AAAA,EACR;AAGA,MAAI,OAAO,WAAW,OAAO,eAAe,OAAO,WAAW,kBAAkB,aAAa;AAE5F,WAAO;AAAA,EACR;AAIA,MAAI,OAAO,WAAW,YAAY,aAAa;AAC9C,WAAO;AAAA,EACR;AAGA,MAAI,OAAO,WAAW,YAAY,UAAU;AAE3C,QAAI,OAAO,WAAW,cAAc,UAAU;AAE7C,aAAO,WAAW,UAAU;AAAA,IAC7B;AAEA,WAAO;AAAA,EACR;AAEA,MAAI,cAAc,WAAW,SAAS;AACrC,QAAI,UAAU,WAAW,QAAQ,UAAU;AAC1C,aAAO,QAAQ,WAAW,QAAQ,SAAS,IAAI;AAAA,IAChD;AAEA,QAAI,SAAS,WAAW,QAAQ,UAAU;AACzC,aAAO,OAAO,WAAW,QAAQ,SAAS,GAAG;AAAA,IAC9C;AAEA,QAAI,UAAU,WAAW,QAAQ,UAAU;AAC1C,aAAO,WAAW,WAAW,QAAQ,SAAS,IAAI;AAAA,IACnD;AAAA,EACD;AAEA,SAAO;AACR;AA7CgB;;;ACAT,SAAS,kBAAkB;AAKjC,SAAO,YAAY,OAAO,gBAAgB;AAE1C,SAAO,iBAAiB,OAAO,qBAAqB;AACrD;AARgB;;;ACYT,SAAS,gBAAgB,gBAAmE;AAClG,SAAO,mBAAmB,QAAQ,OAAO,mBAAmB,YAAY,YAAY;AACrF;AAFgB;;;ACCT,SAAS,YAAY,gBAA+D;AAC1F,SAAO,mBAAmB,QAAQ,OAAO,mBAAmB,YAAY,YAAY;AACrF;AAFgB;;;ACPT,IAAM,UAAU;", "names": ["range"]}