local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()

WindUI:SetTheme("Dark")

local Players = game:GetService("Players")
local Mouse = Players.LocalPlayer:GetMouse()
local flyKeyDown, flyKeyUp
_G.FLYING = false
_G.flySpeed = 50

local function getRoot(char)
    return char:FindFirstChild('HumanoidRootPart') or char:FindFirstChild('Torso') or char:FindFirstChild('UpperTorso')
end

function sFLY(vfly)
    repeat wait() until Players.LocalPlayer and Players.LocalPlayer.Character and getRoot(Players.LocalPlayer.Character) and Players.LocalPlayer.Character:FindFirstChildOfClass("Humanoid")
    
    if flyKeyDown or flyKeyUp then 
        flyKeyDown:Disconnect() 
        flyKeyUp:Disconnect() 
    end

    local T = getRoot(Players.LocalPlayer.Character)
    local CONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
    local lCONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
    local SPEED = 0

    local function FLY()
        _G.FLYING = true
        local BG = Instance.new('BodyGyro')
        local BV = Instance.new('BodyVelocity')
        BG.P = 9e4
        BG.Parent = T
        BV.Parent = T
        BG.maxTorque = Vector3.new(9e9, 9e9, 9e9)
        BG.cframe = T.CFrame
        BV.velocity = Vector3.new(0, 0, 0)
        BV.maxForce = Vector3.new(9e9, 9e9, 9e9)
        task.spawn(function()
            repeat wait()
                if not vfly and Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid') then
                    Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid').PlatformStand = true
                end
                if CONTROL.L + CONTROL.R ~= 0 or CONTROL.F + CONTROL.B ~= 0 or CONTROL.Q + CONTROL.E ~= 0 then
                    SPEED = _G.flySpeed
                elseif not (CONTROL.L + CONTROL.R ~= 0 or CONTROL.F + CONTROL.B ~= 0 or CONTROL.Q + CONTROL.E ~= 0) and SPEED ~= 0 then
                    SPEED = 0
                end
                if (CONTROL.L + CONTROL.R) ~= 0 or (CONTROL.F + CONTROL.B) ~= 0 or (CONTROL.Q + CONTROL.E) ~= 0 then
                    BV.velocity = ((workspace.CurrentCamera.CoordinateFrame.lookVector * (CONTROL.F + CONTROL.B)) + ((workspace.CurrentCamera.CoordinateFrame * CFrame.new(CONTROL.L + CONTROL.R, (CONTROL.F + CONTROL.B + CONTROL.Q + CONTROL.E) * 0.2, 0).p) - workspace.CurrentCamera.CoordinateFrame.p)) * SPEED
                    lCONTROL = {F = CONTROL.F, B = CONTROL.B, L = CONTROL.L, R = CONTROL.R}
                elseif (CONTROL.L + CONTROL.R) == 0 and (CONTROL.F + CONTROL.B) == 0 and (CONTROL.Q + CONTROL.E) == 0 and SPEED ~= 0 then
                    BV.velocity = ((workspace.CurrentCamera.CoordinateFrame.lookVector * (lCONTROL.F + lCONTROL.B)) + ((workspace.CurrentCamera.CoordinateFrame * CFrame.new(lCONTROL.L + lCONTROL.R, (lCONTROL.F + lCONTROL.B + CONTROL.Q + CONTROL.E) * 0.2, 0).p) - workspace.CurrentCamera.CoordinateFrame.p)) * SPEED
                else
                    BV.velocity = Vector3.new(0, 0, 0)
                end
                BG.cframe = workspace.CurrentCamera.CoordinateFrame
            until not _G.FLYING
            CONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
            lCONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
            SPEED = 0
            BG:Destroy()
            BV:Destroy()
            if Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid') then
                Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid').PlatformStand = false
            end
        end)
    end
    
    flyKeyDown = Mouse.KeyDown:Connect(function(KEY)    
        if KEY:lower() == 'w' then
            CONTROL.F = _G.flySpeed
        elseif KEY:lower() == 's' then
            CONTROL.B = -_G.flySpeed
        elseif KEY:lower() == 'a' then
            CONTROL.L = -_G.flySpeed
        elseif KEY:lower() == 'd' then 
            CONTROL.R = _G.flySpeed
        elseif KEY:lower() == 'e' then
            CONTROL.Q = _G.flySpeed*2
        elseif KEY:lower() == 'q' then
            CONTROL.E = -_G.flySpeed*2
        end
        pcall(function() workspace.CurrentCamera.CameraType = Enum.CameraType.Track end)
    end)
    
    flyKeyUp = Mouse.KeyUp:Connect(function(KEY)
        if KEY:lower() == 'w' then
            CONTROL.F = 0
        elseif KEY:lower() == 's' then
            CONTROL.B = 0
        elseif KEY:lower() == 'a' then
            CONTROL.L = 0
        elseif KEY:lower() == 'd' then
            CONTROL.R = 0
        elseif KEY:lower() == 'e' then
            CONTROL.Q = 0
        elseif KEY:lower() == 'q' then
            CONTROL.E = 0
        end
    end)
    FLY()
end

-- Item Arrays for Specialized ESP
local GUNS_ARMOR_ITEMS = { "Revolver", "Rifle", "Revolver Ammo", "Rifle Ammo", "Leather Body", "Iron Body", "Thorn Body" }
local CRAFTING_ITEMS = { "Bolt", "Sheet Metal", "Old Radio", "Broken Fan", "Broken Microwave" }
local FUEL_ITEMS = { "Log", "Chair", "Coal", "Fuel Canister", "Oil Barrel", "Biofuel" }
local FOOD_ITEMS = { "Carrot", "Berry", "Morsel", "Steak", "Cooked Morsel", "Cooked Steak" }

local Window = WindUI:CreateWindow({
    Title = "PulseHub Horror",
    Icon = "skull",
    Author = "PulseHub Team",
    Folder = "PulseHub",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    SideBarWidth = 200,
    ScrollBarEnabled = false,
    HideSearchBar = false,
    User = {
        Enabled = true,
        Anonymous = false,
        Callback = function()
            print("User profile clicked")
        end,
    },
})

Window:SetToggleKey(Enum.KeyCode.Insert)

local MainTab = Window:Tab({
    Title = "Main",
    Icon = "home",
    Locked = false,
})

local PlayerTab = Window:Tab({
    Title = "Player",
    Icon = "user",
    Locked = false,
})

local ESPTab = Window:Tab({
    Title = "ESP",
    Icon = "eye",
    Locked = false,
})

local mainSection = MainTab:Section({
    Title = "Visual",
    TextXAlignment = "Left",
    TextSize = 17,
})

local fullBright = MainTab:Toggle({
    Title = "Full Bright",
    Desc = "Makes everything bright",
    Icon = "sun",
    Type = "Checkbox",
    Default = false,
    Callback = function(value)
        local lighting = game:GetService("Lighting")
        local tweenService = game:GetService("TweenService")
        
        local info = TweenInfo.new(
            1.5,
            Enum.EasingStyle.Quint,
            Enum.EasingDirection.InOut,
            0,
            false,
            0
        )
        
        if value then
            local brightness = tweenService:Create(lighting, info, {
                Brightness = 2
            })
            local clockTime = tweenService:Create(lighting, info, {
                ClockTime = 14
            })
            local ambient = tweenService:Create(lighting, info, {
                OutdoorAmbient = Color3.fromRGB(128, 128, 128)
            })
            local fog = tweenService:Create(lighting, info, {
                FogEnd = 100000
            })
            
            brightness:Play()
            clockTime:Play() 
            ambient:Play()
            fog:Play()
            
            lighting.GlobalShadows = false
        else
            local brightness = tweenService:Create(lighting, info, {
                Brightness = 1
            })
            local clockTime = tweenService:Create(lighting, info, {
                ClockTime = 12
            })
            local ambient = tweenService:Create(lighting, info, {
                OutdoorAmbient = Color3.fromRGB(70, 70, 70)
            })
            local fog = tweenService:Create(lighting, info, {
                FogEnd = 100000
            })
            
            brightness:Play()
            clockTime:Play()
            ambient:Play()
            fog:Play()
            
            lighting.GlobalShadows = true
        end
    end
})

local removeFog = MainTab:Button({
    Title = "Remove Fog",
    Desc = "Delete boundaries folder",
    Callback = function()
        if workspace:FindFirstChild("Map") and workspace.Map:FindFirstChild("Boundaries") then
            workspace.Map.Boundaries:Destroy()
            print("Fog boundaries removed!")
        else
            print("Boundaries folder not found")
        end
    end
})

local playerSection = PlayerTab:Section({
    Title = "Player",
    TextXAlignment = "Left",
    TextSize = 17,
})

local walkSpeed = PlayerTab:Slider({
    Title = "Walk Speed",
    Desc = "Change movement speed",
    Step = 1,
    Value = {
        Min = 16,
        Max = 200,
        Default = 16,
    },
    Callback = function(val)
        local speed = tonumber(val) or 16
        if game.Players.LocalPlayer.Character and game.Players.LocalPlayer.Character:FindFirstChild("Humanoid") then
            game.Players.LocalPlayer.Character.Humanoid.WalkSpeed = speed
        end
    end
})

local jumpPower = PlayerTab:Slider({
    Title = "Jump Power",
    Desc = "Change jump height",
    Step = 1,
    Value = {
        Min = 50,
        Max = 300,
        Default = 50,
    },
    Callback = function(val)
        local power = tonumber(val) or 50
        if game.Players.LocalPlayer.Character and game.Players.LocalPlayer.Character:FindFirstChild("Humanoid") then
            game.Players.LocalPlayer.Character.Humanoid.JumpPower = power
        end
    end
})

local noclip = PlayerTab:Toggle({
    Title = "Noclip",
    Desc = "Walk through walls",
    Icon = "ghost",
    Type = "Checkbox",
    Default = false,
    Callback = function(enabled)
        local plr = game.Players.LocalPlayer
        local char = plr.Character
        if char then
            for i, v in pairs(char:GetChildren()) do
                if v:IsA("BasePart") then
                    v.CanCollide = not enabled
                end
            end
        end
    end
})

local infJump = PlayerTab:Toggle({
    Title = "Infinite Jump",
    Desc = "Jump unlimited times",
    Icon = "arrow-up",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.infJump = state
        if state then
            _G.infJumpConnection = game:GetService("UserInputService").JumpRequest:Connect(function()
                if game.Players.LocalPlayer.Character and game.Players.LocalPlayer.Character:FindFirstChild("Humanoid") then
                    game.Players.LocalPlayer.Character.Humanoid:ChangeState("Jumping")
                end
            end)
        else
            if _G.infJumpConnection then
                _G.infJumpConnection:Disconnect()
                _G.infJumpConnection = nil
            end
        end
    end
})

local flySpeed = PlayerTab:Slider({
    Title = "Fly Speed",
    Desc = "Control fly speed",
    Step = 5,
    Value = {
        Min = 10,
        Max = 200,
        Default = 50,
    },
    Callback = function(val)
        _G.flySpeed = tonumber(val) or 50
    end
})

local fly = PlayerTab:Toggle({
    Title = "Fly",
    Desc = "Fly around",
    Icon = "feather",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        if state then
            sFLY(false)
        else
            if _G.FLYING then
                _G.FLYING = false
            end
            if flyKeyDown then flyKeyDown:Disconnect() end
            if flyKeyUp then flyKeyUp:Disconnect() end
        end
    end
})

local godMode = PlayerTab:Toggle({
    Title = "God Mode",
    Desc = "No damage",
    Icon = "shield",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.godMode = state
    end
})

local espSection = ESPTab:Section({
    Title = "ESP",
    TextXAlignment = "Left",
    TextSize = 17,
})

-- Initialize PulseESP global variables
_G.PulseESP = _G.PulseESP or {}
_G.PulseESP.itemHighlights = {}
_G.PulseESP.playerHighlights = {}
_G.PulseESP.monsterHighlights = {}
_G.PulseESP.characterHighlights = {}
_G.PulseESP.gunsArmorHighlights = {}
_G.PulseESP.fuelHighlights = {}
_G.PulseESP.foodHighlights = {}
_G.PulseESP.craftingHighlights = {}
_G.PulseESP.availableItems = {"ALL"}
_G.PulseESP.selectedItem = "ALL"
_G.PulseESP.availableCharacterGroups = {"ALL"}
_G.PulseESP.selectedCharacterGroup = "ALL"
_G.PulseESP.selectedGunsArmorESP = "ALL"
_G.PulseESP.selectedFuelESP = "ALL"
_G.PulseESP.selectedFoodESP = "ALL"
_G.PulseESP.selectedCraftingESP = "ALL"
_G.PulseESP.itemESPActive = false
_G.PulseESP.playerESPActive = false
_G.PulseESP.monsterESPActive = false
_G.PulseESP.characterESPActive = false
_G.PulseESP.gunsArmorESPActive = false
_G.PulseESP.fuelESPActive = false
_G.PulseESP.foodESPActive = false
_G.PulseESP.craftingESPActive = false
_G.PulseESP.renderDistance = 500
_G.PulseESP.espColor = Color3.fromRGB(0, 255, 0)
_G.PulseESP.activeThreads = {}

_G.PulseESP.ESPFolder = Instance.new("Folder", workspace)
_G.PulseESP.ESPFolder.Name = "PulseESP_Visuals"

-- Specialized ESP Folders
_G.PulseESP.GunsArmorESPFolder = Instance.new("Folder", workspace)
_G.PulseESP.GunsArmorESPFolder.Name = "GunsArmorESP"
_G.PulseESP.FuelESPFolder = Instance.new("Folder", workspace)
_G.PulseESP.FuelESPFolder.Name = "FuelESP"
_G.PulseESP.FoodESPFolder = Instance.new("Folder", workspace)
_G.PulseESP.FoodESPFolder.Name = "FoodESP"
_G.PulseESP.CraftingESPFolder = Instance.new("Folder", workspace)
_G.PulseESP.CraftingESPFolder.Name = "CraftingESP"

local playerESP = ESPTab:Toggle({
    Title = "Player ESP",
    Desc = "Highlight other players with distance",
    Icon = "users",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.playerESPActive = state

        if state then
            startESPThread("PlayerESP", function()
                while _G.PulseESP.playerESPActive do
                    pcall(updatePlayerESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["PlayerESP"] then
                task.cancel(_G.PulseESP.activeThreads["PlayerESP"])
                _G.PulseESP.activeThreads["PlayerESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.playerHighlights)
        end
    end
})

_G.PulseESP.ESPFolder = Instance.new("Folder", workspace)
_G.PulseESP.ESPFolder.Name = "PulseESP_Visuals"

local function createBillboardLabel(text, color)
    local billboard = Instance.new("BillboardGui")
    billboard.Size = UDim2.new(0, 120, 0, 50)
    billboard.AlwaysOnTop = true
    billboard.StudsOffset = Vector3.new(0, 3, 0)
    
    local nameLabel = Instance.new("TextLabel", billboard)
    nameLabel.Size = UDim2.new(1, 0, 0.6, 0)
    nameLabel.Position = UDim2.new(0, 0, 0, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = text
    nameLabel.TextColor3 = color or Color3.new(1, 1, 1)
    nameLabel.TextStrokeTransparency = 0
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextScaled = true
    
    local distLabel = Instance.new("TextLabel", billboard)
    distLabel.Size = UDim2.new(1, 0, 0.4, 0)
    distLabel.Position = UDim2.new(0, 0, 0.6, 0)
    distLabel.BackgroundTransparency = 1
    distLabel.Text = ""
    distLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    distLabel.TextStrokeTransparency = 0.3
    distLabel.Font = Enum.Font.SourceSans
    distLabel.TextScaled = true
    
    return billboard, nameLabel, distLabel
end

local function startESPThread(name, func)
    if _G.PulseESP.activeThreads[name] then
        task.cancel(_G.PulseESP.activeThreads[name])
    end
    _G.PulseESP.activeThreads[name] = task.spawn(func)
end

local function clearESPHighlights(highlightTable)
    for obj, data in pairs(highlightTable) do
        pcall(function()
            if data.highlight then data.highlight:Destroy() end
            if data.billboard then data.billboard:Destroy() end
        end)
    end
    table.clear(highlightTable)
end



local function scanAvailableCharacterGroups()
    local groups = {}
    local uniqueNames = {}

    local charactersFolder = workspace:FindFirstChild("Characters")
    if not charactersFolder then return {"ALL"} end

    pcall(function()
        for _, group in ipairs(charactersFolder:GetChildren()) do
            if (group:IsA("Folder") or group:IsA("Model")) and group.Name and group.Name ~= "" then
                local groupName = tostring(group.Name)
                if not uniqueNames[groupName] then
                    uniqueNames[groupName] = true
                    table.insert(groups, groupName)
                end
            end
        end
    end)

    pcall(function()
        table.sort(groups, function(a, b)
            return tostring(a) < tostring(b)
        end)
    end)

    local result = {"ALL"}
    for i = 1, math.min(#groups, 25) do
        table.insert(result, groups[i])
    end

    return result
end



local function updatePlayerESP()
    if not _G.PulseESP.playerESPActive then return end
    
    local localPlayer = game.Players.LocalPlayer
    if not localPlayer.Character or not localPlayer.Character:FindFirstChild("HumanoidRootPart") then return end
    
    local playerPos = localPlayer.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentPlayers = {}
    
    for _, player in ipairs(game.Players:GetPlayers()) do
        if player ~= localPlayer and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local distance = (player.Character.HumanoidRootPart.Position - playerPos).Magnitude
            
            if distance <= maxDistance then
                currentPlayers[player] = true
                
                if not _G.PulseESP.playerHighlights[player] then
                    local highlight = Instance.new("Highlight")
                    highlight.Adornee = player.Character
                    highlight.FillColor = Color3.fromRGB(255, 100, 100)
                    highlight.OutlineColor = Color3.fromRGB(255, 100, 100)
                    highlight.FillTransparency = 0.5
                    highlight.OutlineTransparency = 0
                    highlight.Parent = _G.PulseESP.ESPFolder
                    
                    local billboard, nameLabel, distLabel = createBillboardLabel(player.Name, Color3.fromRGB(255, 100, 100))
                    billboard.Adornee = player.Character.HumanoidRootPart
                    billboard.Parent = _G.PulseESP.ESPFolder
                    
                    _G.PulseESP.playerHighlights[player] = {
                        highlight = highlight,
                        billboard = billboard,
                        nameLabel = nameLabel,
                        distLabel = distLabel
                    }
                end
                
                if _G.PulseESP.playerHighlights[player].distLabel then
                    _G.PulseESP.playerHighlights[player].distLabel.Text = "[" .. math.floor(distance) .. "m]"
                end
            end
        end
    end
    
    for player, data in pairs(_G.PulseESP.playerHighlights) do
        if not currentPlayers[player] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.playerHighlights[player] = nil
        end
    end
end

local function updateMonsterESP()
    if not _G.PulseESP.monsterESPActive then return end
    
    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end
    
    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentMonsters = {}
    
    local monsterNames = {"Monster", "Zombie", "Enemy", "Boss", "Creature", "Beast"}
    
    for _, folder in ipairs(workspace:GetChildren()) do
        if folder:IsA("Folder") or folder:IsA("Model") then
            for _, obj in ipairs(folder:GetChildren()) do
                if obj:IsA("Model") and obj.PrimaryPart then
                    local objName = obj.Name:lower()
                    local isMonster = false
                    
                    for _, monsterName in ipairs(monsterNames) do
                        if objName:find(monsterName:lower()) then
                            isMonster = true
                            break
                        end
                    end
                    
                    if isMonster then
                        local distance = (obj.PrimaryPart.Position - playerPos).Magnitude
                        
                        if distance <= maxDistance then
                            currentMonsters[obj] = true
                            
                            if not _G.PulseESP.monsterHighlights[obj] then
                                local highlight = Instance.new("Highlight")
                                highlight.Adornee = obj
                                highlight.FillColor = Color3.fromRGB(255, 0, 0)
                                highlight.OutlineColor = Color3.fromRGB(255, 0, 0)
                                highlight.FillTransparency = 0.4
                                highlight.OutlineTransparency = 0
                                highlight.Parent = _G.PulseESP.ESPFolder
                                
                                local billboard, nameLabel, distLabel = createBillboardLabel(obj.Name, Color3.fromRGB(255, 0, 0))
                                billboard.Adornee = obj.PrimaryPart
                                billboard.Parent = _G.PulseESP.ESPFolder
                                
                                _G.PulseESP.monsterHighlights[obj] = {
                                    highlight = highlight,
                                    billboard = billboard,
                                    nameLabel = nameLabel,
                                    distLabel = distLabel
                                }
                            end
                            
                            if _G.PulseESP.monsterHighlights[obj].distLabel then
                                _G.PulseESP.monsterHighlights[obj].distLabel.Text = "[" .. math.floor(distance) .. "m]"
                            end
                        end
                    end
                end
            end
        end
    end
    
    for obj, data in pairs(_G.PulseESP.monsterHighlights) do
        if not currentMonsters[obj] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.monsterHighlights[obj] = nil
        end
    end
end

local function updateCharacterESP()
    if not _G.PulseESP.characterESPActive then return end

    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end

    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentCharacters = {}

    local charactersFolder = workspace:FindFirstChild("Characters")
    if not charactersFolder then return end

    pcall(function()
        for _, group in ipairs(charactersFolder:GetChildren()) do
            if (group:IsA("Folder") or group:IsA("Model")) and group.Name and group.Name ~= "" then
                local groupName = tostring(group.Name)

                -- Check if we should highlight this group based on selection
                if _G.PulseESP.selectedCharacterGroup == "ALL" or groupName == _G.PulseESP.selectedCharacterGroup then
                    for _, character in ipairs(group:GetChildren()) do
                        if character:IsA("Model") and character.PrimaryPart then
                            local distance = (character.PrimaryPart.Position - playerPos).Magnitude

                            if distance <= maxDistance then
                                currentCharacters[character] = true

                                if not _G.PulseESP.characterHighlights[character] then
                                    local highlight = Instance.new("Highlight")
                                    highlight.Adornee = character
                                    highlight.FillColor = Color3.fromRGB(255, 255, 0)
                                    highlight.OutlineColor = Color3.fromRGB(255, 255, 0)
                                    highlight.FillTransparency = 0.5
                                    highlight.OutlineTransparency = 0
                                    highlight.Parent = _G.PulseESP.ESPFolder

                                    -- Include group name in the label for better identification
                                    local characterLabel = character.Name .. " (" .. groupName .. ")"
                                    local billboard, nameLabel, distLabel = createBillboardLabel(characterLabel, Color3.fromRGB(255, 255, 0))
                                    billboard.Adornee = character.PrimaryPart
                                    billboard.Parent = _G.PulseESP.ESPFolder

                                    _G.PulseESP.characterHighlights[character] = {
                                        highlight = highlight,
                                        billboard = billboard,
                                        nameLabel = nameLabel,
                                        distLabel = distLabel
                                    }
                                end

                                if _G.PulseESP.characterHighlights[character].distLabel then
                                    _G.PulseESP.characterHighlights[character].distLabel.Text = "[" .. math.floor(distance) .. "m]"
                                end
                            end
                        end
                    end
                end
            end
        end
    end)

    for character, data in pairs(_G.PulseESP.characterHighlights) do
        if not currentCharacters[character] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.characterHighlights[character] = nil
        end
    end
end

-- Specialized ESP Functions
local function updateSpecializedESP(itemArray, selectedItem, highlightTable, folder, color, espName)
    if not _G.PulseESP[espName .. "Active"] then return end

    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end

    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentItems = {}

    local workspaceItems = workspace:FindFirstChild("Items")
    if not workspaceItems then return end

    pcall(function()
        for _, obj in ipairs(workspaceItems:GetChildren()) do
            if obj:IsA("Model") and obj.Name and obj.Name ~= "" and obj.PrimaryPart then
                local objName = tostring(obj.Name)

                -- Check if item is in the category array
                local isInCategory = false
                for _, categoryItem in ipairs(itemArray) do
                    if objName == categoryItem then
                        isInCategory = true
                        break
                    end
                end

                if isInCategory and (selectedItem == "ALL" or objName == selectedItem) then
                    local distance = (obj.PrimaryPart.Position - playerPos).Magnitude

                    if distance <= maxDistance then
                        currentItems[obj] = true

                        if not highlightTable[obj] then
                            local highlight = Instance.new("Highlight")
                            highlight.Adornee = obj
                            highlight.FillColor = color
                            highlight.OutlineColor = color
                            highlight.FillTransparency = 0.6
                            highlight.OutlineTransparency = 0
                            highlight.Parent = folder

                            local billboard, nameLabel, distLabel = createBillboardLabel(objName, color)
                            billboard.Adornee = obj.PrimaryPart
                            billboard.Parent = folder

                            highlightTable[obj] = {
                                highlight = highlight,
                                billboard = billboard,
                                nameLabel = nameLabel,
                                distLabel = distLabel
                            }
                        end

                        if highlightTable[obj].distLabel then
                            highlightTable[obj].distLabel.Text = "[" .. math.floor(distance) .. "m]"
                        end
                    end
                end
            end
        end
    end)

    for obj, data in pairs(highlightTable) do
        if not currentItems[obj] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            highlightTable[obj] = nil
        end
    end
end

-- Individual ESP Update Functions
local function updateGunsArmorESP()
    updateSpecializedESP(GUNS_ARMOR_ITEMS, _G.PulseESP.selectedGunsArmorESP, _G.PulseESP.gunsArmorHighlights, _G.PulseESP.GunsArmorESPFolder, Color3.fromRGB(255, 0, 0), "gunsArmorESP")
end

local function updateFuelESP()
    updateSpecializedESP(FUEL_ITEMS, _G.PulseESP.selectedFuelESP, _G.PulseESP.fuelHighlights, _G.PulseESP.FuelESPFolder, Color3.fromRGB(255, 165, 0), "fuelESP")
end

local function updateFoodESP()
    updateSpecializedESP(FOOD_ITEMS, _G.PulseESP.selectedFoodESP, _G.PulseESP.foodHighlights, _G.PulseESP.FoodESPFolder, Color3.fromRGB(0, 255, 0), "foodESP")
end

local function updateCraftingESP()
    updateSpecializedESP(CRAFTING_ITEMS, _G.PulseESP.selectedCraftingESP, _G.PulseESP.craftingHighlights, _G.PulseESP.CraftingESPFolder, Color3.fromRGB(0, 100, 255), "craftingESP")
end

local characterSelector = ESPTab:Dropdown({
    Title = "Character ESP Select",
    Desc = "Choose which character group to highlight",
    Values = scanAvailableCharacterGroups(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedCharacterGroup = tostring(option)
        if _G.PulseESP.characterESPActive then
            updateCharacterESP()
        end
    end
})

-- Specialized Item ESP Section
local espItemSection = ESPTab:Section({
    Title = "Specialized Item ESP",
    TextXAlignment = "Left",
    TextSize = 17,
})

-- Gun & Armor ESP
local gunsArmorSelector = ESPTab:Dropdown({
    Title = "Gun & Armor ESP Select",
    Desc = "Choose which gun/armor item to highlight",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(GUNS_ARMOR_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedGunsArmorESP = tostring(option)
        if _G.PulseESP.gunsArmorESPActive then
            updateGunsArmorESP()
        end
    end
})

local gunsArmorESP = ESPTab:Toggle({
    Title = "Gun & Armor ESP",
    Desc = "Highlight guns and armor items (Red)",
    Icon = "shield",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.gunsArmorESPActive = state

        if state then
            startESPThread("GunsArmorESP", function()
                while _G.PulseESP.gunsArmorESPActive do
                    pcall(updateGunsArmorESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["GunsArmorESP"] then
                task.cancel(_G.PulseESP.activeThreads["GunsArmorESP"])
                _G.PulseESP.activeThreads["GunsArmorESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.gunsArmorHighlights)
        end
    end
})

-- Fuel ESP
local fuelSelector = ESPTab:Dropdown({
    Title = "Fuel ESP Select",
    Desc = "Choose which fuel item to highlight",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(FUEL_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedFuelESP = tostring(option)
        if _G.PulseESP.fuelESPActive then
            updateFuelESP()
        end
    end
})

local fuelESP = ESPTab:Toggle({
    Title = "Fuel ESP",
    Desc = "Highlight fuel items (Orange)",
    Icon = "flame",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.fuelESPActive = state

        if state then
            startESPThread("FuelESP", function()
                while _G.PulseESP.fuelESPActive do
                    pcall(updateFuelESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["FuelESP"] then
                task.cancel(_G.PulseESP.activeThreads["FuelESP"])
                _G.PulseESP.activeThreads["FuelESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.fuelHighlights)
        end
    end
})

-- Food ESP
local foodSelector = ESPTab:Dropdown({
    Title = "Food ESP Select",
    Desc = "Choose which food item to highlight",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(FOOD_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedFoodESP = tostring(option)
        if _G.PulseESP.foodESPActive then
            updateFoodESP()
        end
    end
})

local foodESP = ESPTab:Toggle({
    Title = "Food ESP",
    Desc = "Highlight food items (Green)",
    Icon = "apple",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.foodESPActive = state

        if state then
            startESPThread("FoodESP", function()
                while _G.PulseESP.foodESPActive do
                    pcall(updateFoodESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["FoodESP"] then
                task.cancel(_G.PulseESP.activeThreads["FoodESP"])
                _G.PulseESP.activeThreads["FoodESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.foodHighlights)
        end
    end
})

-- Crafting ESP
local craftingSelector = ESPTab:Dropdown({
    Title = "Crafting ESP Select",
    Desc = "Choose which crafting item to highlight",
    Values = (function()
        local values = {"ALL"}
        for _, item in ipairs(CRAFTING_ITEMS) do
            table.insert(values, item)
        end
        return values
    end)(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedCraftingESP = tostring(option)
        if _G.PulseESP.craftingESPActive then
            updateCraftingESP()
        end
    end
})

local craftingESP = ESPTab:Toggle({
    Title = "Crafting ESP",
    Desc = "Highlight crafting items (Blue)",
    Icon = "wrench",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.craftingESPActive = state

        if state then
            startESPThread("CraftingESP", function()
                while _G.PulseESP.craftingESPActive do
                    pcall(updateCraftingESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["CraftingESP"] then
                task.cancel(_G.PulseESP.activeThreads["CraftingESP"])
                _G.PulseESP.activeThreads["CraftingESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.craftingHighlights)
        end
    end
})

local monsterESP = ESPTab:Toggle({
    Title = "Monster ESP",
    Desc = "Highlight monsters and enemies",
    Icon = "skull",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.monsterESPActive = state

        if state then
            startESPThread("MonsterESP", function()
                while _G.PulseESP.monsterESPActive do
                    pcall(updateMonsterESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["MonsterESP"] then
                task.cancel(_G.PulseESP.activeThreads["MonsterESP"])
                _G.PulseESP.activeThreads["MonsterESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.monsterHighlights)
        end
    end
})

local characterESP = ESPTab:Toggle({
    Title = "Character ESP",
    Desc = "Highlight all character groups in workspace.Characters",
    Icon = "users",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.characterESPActive = state

        if state then
            _G.PulseESP.availableCharacterGroups = scanAvailableCharacterGroups()

            startESPThread("CharacterESP", function()
                while _G.PulseESP.characterESPActive do
                    pcall(updateCharacterESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["CharacterESP"] then
                task.cancel(_G.PulseESP.activeThreads["CharacterESP"])
                _G.PulseESP.activeThreads["CharacterESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.characterHighlights)
        end
    end
})

local renderDistanceSlider = ESPTab:Slider({
    Title = "ESP Distance",
    Desc = "Maximum ESP render distance",
    Step = 50,
    Value = {
        Min = 100,
        Max = 2000,
        Default = 500,
    },
    Callback = function(value)
        _G.PulseESP.renderDistance = tonumber(value) or 500
    end
})

local espColor = ESPTab:Colorpicker({
    Title = "Item ESP Color",
    Desc = "Color for item highlights",
    Default = Color3.fromRGB(0, 255, 0),
    Transparency = 0,
    Callback = function(color)
        _G.PulseESP.espColor = color
    end
})

local refreshItemsButton = ESPTab:Button({
    Title = "Refresh Item List",
    Desc = "Update available items in dropdown",
    Callback = function()
        _G.PulseESP.availableItems = scanAvailableItems()
        print("Available items refreshed: " .. table.concat(_G.PulseESP.availableItems, ", "))
    end
})

local refreshCharacterGroupsButton = ESPTab:Button({
    Title = "Refresh Character Groups",
    Desc = "Update available character groups in dropdown",
    Callback = function()
        _G.PulseESP.availableCharacterGroups = scanAvailableCharacterGroups()
        print("Available character groups refreshed: " .. table.concat(_G.PulseESP.availableCharacterGroups, ", "))
    end
})

local listCharactersButton = ESPTab:Button({
    Title = "List All Characters",
    Desc = "Display all detected characters with distances",
    Callback = function()
        local player = game.Players.LocalPlayer
        if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
            print("Player character not found!")
            return
        end

        local playerPos = player.Character.HumanoidRootPart.Position
        local charactersFolder = workspace:FindFirstChild("Characters")
        if not charactersFolder then
            print("Characters folder not found in workspace!")
            return
        end

        local characterList = {}
        local totalCount = 0

        pcall(function()
            for _, group in ipairs(charactersFolder:GetChildren()) do
                if (group:IsA("Folder") or group:IsA("Model")) and group.Name and group.Name ~= "" then
                    local groupName = tostring(group.Name)
                    local groupCount = 0

                    for _, character in ipairs(group:GetChildren()) do
                        if character:IsA("Model") and character.PrimaryPart then
                            local distance = (character.PrimaryPart.Position - playerPos).Magnitude
                            table.insert(characterList, {
                                name = character.Name,
                                group = groupName,
                                distance = math.floor(distance)
                            })
                            groupCount = groupCount + 1
                            totalCount = totalCount + 1
                        end
                    end

                    if groupCount > 0 then
                        print("Group '" .. groupName .. "': " .. groupCount .. " characters")
                    end
                end
            end
        end)

        -- Sort by distance
        table.sort(characterList, function(a, b) return a.distance < b.distance end)

        print("\n=== CHARACTER ESP LIST ===")
        print("Total characters found: " .. totalCount)
        print("Selected group: " .. (_G.PulseESP.selectedCharacterGroup or "ALL"))
        print("\nClosest 10 characters:")

        for i = 1, math.min(10, #characterList) do
            local char = characterList[i]
            print(i .. ". " .. char.name .. " (" .. char.group .. ") - " .. char.distance .. "m")
        end

        if #characterList > 10 then
            print("... and " .. (#characterList - 10) .. " more characters")
        end
        print("========================\n")
    end
})

-- ESP Control Buttons
local espControlSection = ESPTab:Section({
    Title = "ESP Controls",
    TextXAlignment = "Left",
    TextSize = 17,
})

local clearAllESP = ESPTab:Button({
    Title = "Clear All ESP",
    Desc = "Remove all ESP highlights",
    Callback = function()
        clearESPHighlights(_G.PulseESP.itemHighlights)
        clearESPHighlights(_G.PulseESP.playerHighlights)
        clearESPHighlights(_G.PulseESP.monsterHighlights)
        clearESPHighlights(_G.PulseESP.characterHighlights)
        clearESPHighlights(_G.PulseESP.gunsArmorHighlights)
        clearESPHighlights(_G.PulseESP.fuelHighlights)
        clearESPHighlights(_G.PulseESP.foodHighlights)
        clearESPHighlights(_G.PulseESP.craftingHighlights)
        print("All ESP highlights cleared!")
    end
})

local refreshESPButton = ESPTab:Button({
    Title = "Refresh ESP Cache",
    Desc = "Update item detection for all ESP systems",
    Callback = function()
        -- Clear existing highlights and refresh
        clearESPHighlights(_G.PulseESP.gunsArmorHighlights)
        clearESPHighlights(_G.PulseESP.fuelHighlights)
        clearESPHighlights(_G.PulseESP.foodHighlights)
        clearESPHighlights(_G.PulseESP.craftingHighlights)
        print("ESP cache refreshed!")
    end
})

WindUI:Popup({
    Title = "Loaded",
    Icon = "check-circle",
    Content = "Script loaded! Press INSERT to toggle.",
    Buttons = {
        {
            Title = "OK",
            Callback = function()
            end,
            Variant = "Primary",
        }
    }
})
