local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()

WindUI:SetTheme("Dark")

local Players = game:GetService("Players")
local Mouse = Players.LocalPlayer:GetMouse()
local flyKeyDown, flyKeyUp
_G.FLYING = false
_G.flySpeed = 50

local function getRoot(char)
    return char:FindFirstChild('HumanoidRootPart') or char:FindFirstChild('Torso') or char:FindFirstChild('UpperTorso')
end

function sFLY(vfly)
    repeat wait() until Players.LocalPlayer and Players.LocalPlayer.Character and getRoot(Players.LocalPlayer.Character) and Players.LocalPlayer.Character:FindFirstChildOfClass("Humanoid")
    
    if flyKeyDown or flyKeyUp then 
        flyKeyDown:Disconnect() 
        flyKeyUp:Disconnect() 
    end

    local T = getRoot(Players.LocalPlayer.Character)
    local CONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
    local lCONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
    local SPEED = 0

    local function FLY()
        _G.FLYING = true
        local BG = Instance.new('BodyGyro')
        local BV = Instance.new('BodyVelocity')
        BG.P = 9e4
        BG.Parent = T
        BV.Parent = T
        BG.maxTorque = Vector3.new(9e9, 9e9, 9e9)
        BG.cframe = T.CFrame
        BV.velocity = Vector3.new(0, 0, 0)
        BV.maxForce = Vector3.new(9e9, 9e9, 9e9)
        task.spawn(function()
            repeat wait()
                if not vfly and Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid') then
                    Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid').PlatformStand = true
                end
                if CONTROL.L + CONTROL.R ~= 0 or CONTROL.F + CONTROL.B ~= 0 or CONTROL.Q + CONTROL.E ~= 0 then
                    SPEED = _G.flySpeed
                elseif not (CONTROL.L + CONTROL.R ~= 0 or CONTROL.F + CONTROL.B ~= 0 or CONTROL.Q + CONTROL.E ~= 0) and SPEED ~= 0 then
                    SPEED = 0
                end
                if (CONTROL.L + CONTROL.R) ~= 0 or (CONTROL.F + CONTROL.B) ~= 0 or (CONTROL.Q + CONTROL.E) ~= 0 then
                    BV.velocity = ((workspace.CurrentCamera.CoordinateFrame.lookVector * (CONTROL.F + CONTROL.B)) + ((workspace.CurrentCamera.CoordinateFrame * CFrame.new(CONTROL.L + CONTROL.R, (CONTROL.F + CONTROL.B + CONTROL.Q + CONTROL.E) * 0.2, 0).p) - workspace.CurrentCamera.CoordinateFrame.p)) * SPEED
                    lCONTROL = {F = CONTROL.F, B = CONTROL.B, L = CONTROL.L, R = CONTROL.R}
                elseif (CONTROL.L + CONTROL.R) == 0 and (CONTROL.F + CONTROL.B) == 0 and (CONTROL.Q + CONTROL.E) == 0 and SPEED ~= 0 then
                    BV.velocity = ((workspace.CurrentCamera.CoordinateFrame.lookVector * (lCONTROL.F + lCONTROL.B)) + ((workspace.CurrentCamera.CoordinateFrame * CFrame.new(lCONTROL.L + lCONTROL.R, (lCONTROL.F + lCONTROL.B + CONTROL.Q + CONTROL.E) * 0.2, 0).p) - workspace.CurrentCamera.CoordinateFrame.p)) * SPEED
                else
                    BV.velocity = Vector3.new(0, 0, 0)
                end
                BG.cframe = workspace.CurrentCamera.CoordinateFrame
            until not _G.FLYING
            CONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
            lCONTROL = {F = 0, B = 0, L = 0, R = 0, Q = 0, E = 0}
            SPEED = 0
            BG:Destroy()
            BV:Destroy()
            if Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid') then
                Players.LocalPlayer.Character:FindFirstChildOfClass('Humanoid').PlatformStand = false
            end
        end)
    end
    
    flyKeyDown = Mouse.KeyDown:Connect(function(KEY)
        if KEY:lower() == 'w' then
            CONTROL.F = _G.flySpeed
        elseif KEY:lower() == 's' then
            CONTROL.B = -_G.flySpeed
        elseif KEY:lower() == 'a' then
            CONTROL.L = -_G.flySpeed
        elseif KEY:lower() == 'd' then 
            CONTROL.R = _G.flySpeed
        elseif KEY:lower() == 'e' then
            CONTROL.Q = _G.flySpeed*2
        elseif KEY:lower() == 'q' then
            CONTROL.E = -_G.flySpeed*2
        end
        pcall(function() workspace.CurrentCamera.CameraType = Enum.CameraType.Track end)
    end)
    
    flyKeyUp = Mouse.KeyUp:Connect(function(KEY)
        if KEY:lower() == 'w' then
            CONTROL.F = 0
        elseif KEY:lower() == 's' then
            CONTROL.B = 0
        elseif KEY:lower() == 'a' then
            CONTROL.L = 0
        elseif KEY:lower() == 'd' then
            CONTROL.R = 0
        elseif KEY:lower() == 'e' then
            CONTROL.Q = 0
        elseif KEY:lower() == 'q' then
            CONTROL.E = 0
        end
    end)
    FLY()
end

local Window = WindUI:CreateWindow({
    Title = "PulseHub Horror",
    Icon = "skull",
    Author = "PulseHub Team",
    Folder = "PulseHub",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    SideBarWidth = 200,
    ScrollBarEnabled = false,
    HideSearchBar = false,
    User = {
        Enabled = true,
        Anonymous = false,
        Callback = function()
            print("User profile clicked")
        end,
    },
})

Window:SetToggleKey(Enum.KeyCode.Insert)

local MainTab = Window:Tab({
    Title = "Main",
    Icon = "home",
    Locked = false,
})

local PlayerTab = Window:Tab({
    Title = "Player",
    Icon = "user",
    Locked = false,
})

local ESPTab = Window:Tab({
    Title = "ESP",
    Icon = "eye",
    Locked = false,
})

local mainSection = MainTab:Section({
    Title = "Visual",
    TextXAlignment = "Left",
    TextSize = 17,
})

local fullBright = MainTab:Toggle({
    Title = "Full Bright",
    Desc = "Makes everything bright",
    Icon = "sun",
    Type = "Checkbox",
    Default = false,
    Callback = function(value)
        local lighting = game:GetService("Lighting")
        local tweenService = game:GetService("TweenService")
        
        local info = TweenInfo.new(
            1.5,
            Enum.EasingStyle.Quint,
            Enum.EasingDirection.InOut,
            0,
            false,
            0
        )
        
        if value then
            local brightness = tweenService:Create(lighting, info, {
                Brightness = 2
            })
            local clockTime = tweenService:Create(lighting, info, {
                ClockTime = 14
            })
            local ambient = tweenService:Create(lighting, info, {
                OutdoorAmbient = Color3.fromRGB(128, 128, 128)
            })
            local fog = tweenService:Create(lighting, info, {
                FogEnd = 100000
            })
            
            brightness:Play()
            clockTime:Play() 
            ambient:Play()
            fog:Play()
            
            lighting.GlobalShadows = false
        else
            local brightness = tweenService:Create(lighting, info, {
                Brightness = 1
            })
            local clockTime = tweenService:Create(lighting, info, {
                ClockTime = 12
            })
            local ambient = tweenService:Create(lighting, info, {
                OutdoorAmbient = Color3.fromRGB(70, 70, 70)
            })
            local fog = tweenService:Create(lighting, info, {
                FogEnd = 100000
            })
            
            brightness:Play()
            clockTime:Play()
            ambient:Play()
            fog:Play()
            
            lighting.GlobalShadows = true
        end
    end
})

local removeFog = MainTab:Button({
    Title = "Remove Fog",
    Desc = "Delete boundaries folder",
    Callback = function()
        if workspace:FindFirstChild("Map") and workspace.Map:FindFirstChild("Boundaries") then
            workspace.Map.Boundaries:Destroy()
            print("Fog boundaries removed!")
        else
            print("Boundaries folder not found")
        end
    end
})

local playerSection = PlayerTab:Section({
    Title = "Player",
    TextXAlignment = "Left",
    TextSize = 17,
})

local walkSpeed = PlayerTab:Slider({
    Title = "Walk Speed",
    Desc = "Change movement speed",
    Step = 1,
    Value = {
        Min = 16,
        Max = 200,
        Default = 16,
    },
    Callback = function(val)
        local speed = tonumber(val) or 16
        if game.Players.LocalPlayer.Character and game.Players.LocalPlayer.Character:FindFirstChild("Humanoid") then
            game.Players.LocalPlayer.Character.Humanoid.WalkSpeed = speed
        end
    end
})

local jumpPower = PlayerTab:Slider({
    Title = "Jump Power",
    Desc = "Change jump height",
    Step = 1,
    Value = {
        Min = 50,
        Max = 300,
        Default = 50,
    },
    Callback = function(val)
        local power = tonumber(val) or 50
        if game.Players.LocalPlayer.Character and game.Players.LocalPlayer.Character:FindFirstChild("Humanoid") then
            game.Players.LocalPlayer.Character.Humanoid.JumpPower = power
        end
    end
})

local noclip = PlayerTab:Toggle({
    Title = "Noclip",
    Desc = "Walk through walls",
    Icon = "ghost",
    Type = "Checkbox",
    Default = false,
    Callback = function(enabled)
        local plr = game.Players.LocalPlayer
        local char = plr.Character
        if char then
            for i, v in pairs(char:GetChildren()) do
                if v:IsA("BasePart") then
                    v.CanCollide = not enabled
                end
            end
        end
    end
})

local infJump = PlayerTab:Toggle({
    Title = "Infinite Jump",
    Desc = "Jump unlimited times",
    Icon = "arrow-up",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.infJump = state
        if state then
            _G.infJumpConnection = game:GetService("UserInputService").JumpRequest:Connect(function()
                if game.Players.LocalPlayer.Character and game.Players.LocalPlayer.Character:FindFirstChild("Humanoid") then
                    game.Players.LocalPlayer.Character.Humanoid:ChangeState("Jumping")
                end
            end)
        else
            if _G.infJumpConnection then
                _G.infJumpConnection:Disconnect()
                _G.infJumpConnection = nil
            end
        end
    end
})

local flySpeed = PlayerTab:Slider({
    Title = "Fly Speed",
    Desc = "Control fly speed",
    Step = 5,
    Value = {
        Min = 10,
        Max = 200,
        Default = 50,
    },
    Callback = function(val)
        _G.flySpeed = tonumber(val) or 50
    end
})

local fly = PlayerTab:Toggle({
    Title = "Fly",
    Desc = "Fly around",
    Icon = "feather",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        if state then
            sFLY(false)
        else
            if _G.FLYING then
                _G.FLYING = false
            end
            if flyKeyDown then flyKeyDown:Disconnect() end
            if flyKeyUp then flyKeyUp:Disconnect() end
        end
    end
})

local godMode = PlayerTab:Toggle({
    Title = "God Mode",
    Desc = "No damage",
    Icon = "shield",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.godMode = state
    end
})

local espSection = ESPTab:Section({
    Title = "ESP",
    TextXAlignment = "Left",
    TextSize = 17,
})

local playerESP = ESPTab:Toggle({
    Title = "Player ESP",
    Desc = "Highlight other players with distance",
    Icon = "users",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.playerESPActive = state
        
        if state then
            startESPThread("PlayerESP", function()
                while _G.PulseESP.playerESPActive do
                    pcall(updatePlayerESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["PlayerESP"] then
                task.cancel(_G.PulseESP.activeThreads["PlayerESP"])
                _G.PulseESP.activeThreads["PlayerESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.playerHighlights)
        end
    end
})

_G.PulseESP = _G.PulseESP or {}
_G.PulseESP.itemHighlights = {}
_G.PulseESP.playerHighlights = {}
_G.PulseESP.monsterHighlights = {}
_G.PulseESP.characterHighlights = {}
_G.PulseESP.availableItems = {"ALL"}
_G.PulseESP.selectedItem = "ALL"
_G.PulseESP.itemESPActive = false
_G.PulseESP.playerESPActive = false
_G.PulseESP.monsterESPActive = false
_G.PulseESP.characterESPActive = false
_G.PulseESP.renderDistance = 500
_G.PulseESP.espColor = Color3.fromRGB(0, 255, 0)
_G.PulseESP.activeThreads = {}
_G.flySpeed = 50

_G.PulseESP.ESPFolder = Instance.new("Folder", workspace)
_G.PulseESP.ESPFolder.Name = "PulseESP_Visuals"

local function createBillboardLabel(text, color)
    local billboard = Instance.new("BillboardGui")
    billboard.Size = UDim2.new(0, 120, 0, 50)
    billboard.AlwaysOnTop = true
    billboard.StudsOffset = Vector3.new(0, 3, 0)
    
    local nameLabel = Instance.new("TextLabel", billboard)
    nameLabel.Size = UDim2.new(1, 0, 0.6, 0)
    nameLabel.Position = UDim2.new(0, 0, 0, 0)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = text
    nameLabel.TextColor3 = color or Color3.new(1, 1, 1)
    nameLabel.TextStrokeTransparency = 0
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextScaled = true
    
    local distLabel = Instance.new("TextLabel", billboard)
    distLabel.Size = UDim2.new(1, 0, 0.4, 0)
    distLabel.Position = UDim2.new(0, 0, 0.6, 0)
    distLabel.BackgroundTransparency = 1
    distLabel.Text = ""
    distLabel.TextColor3 = Color3.new(0.8, 0.8, 0.8)
    distLabel.TextStrokeTransparency = 0.3
    distLabel.Font = Enum.Font.SourceSans
    distLabel.TextScaled = true
    
    return billboard, nameLabel, distLabel
end

local function startESPThread(name, func)
    if _G.PulseESP.activeThreads[name] then
        task.cancel(_G.PulseESP.activeThreads[name])
    end
    _G.PulseESP.activeThreads[name] = task.spawn(func)
end

local function clearESPHighlights(highlightTable)
    for obj, data in pairs(highlightTable) do
        pcall(function()
            if data.highlight then data.highlight:Destroy() end
            if data.billboard then data.billboard:Destroy() end
        end)
    end
    table.clear(highlightTable)
end

local function scanAvailableItems()
    local items = {}
    local uniqueNames = {}
    
    local workspaceItems = workspace:FindFirstChild("Items")
    if not workspaceItems then return {"ALL"} end
    
    pcall(function()
        for _, obj in ipairs(workspaceItems:GetChildren()) do
            if obj:IsA("Model") and obj.Name and obj.Name ~= "" then
                local objName = tostring(obj.Name)
                if not uniqueNames[objName] then
                    uniqueNames[objName] = true
                    table.insert(items, objName)
                end
            end
        end
    end)
    
    pcall(function()
        table.sort(items, function(a, b)
            return tostring(a) < tostring(b)
        end)
    end)
    
    local result = {"ALL"}
    for i = 1, math.min(#items, 25) do
        table.insert(result, items[i])
    end
    
    return result
end

local function updateItemESP()
    if not _G.PulseESP.itemESPActive then return end
    
    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end
    
    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    
    local workspaceItems = workspace:FindFirstChild("Items")
    if not workspaceItems then return end
    
    local currentItems = {}
    
    pcall(function()
        for _, obj in ipairs(workspaceItems:GetChildren()) do
            if obj:IsA("Model") and obj.PrimaryPart and obj.Name ~= "" then
                local objName = tostring(obj.Name)
                
                if _G.PulseESP.selectedItem == "ALL" or objName == _G.PulseESP.selectedItem then
                    local distance = (obj.PrimaryPart.Position - playerPos).Magnitude
                    
                    if distance <= maxDistance then
                        currentItems[obj] = true
                        
                        if not _G.PulseESP.itemHighlights[obj] then
                            local highlight = Instance.new("Highlight")
                            highlight.Adornee = obj
                            highlight.FillColor = _G.PulseESP.espColor
                            highlight.OutlineColor = _G.PulseESP.espColor
                            highlight.FillTransparency = 0.6
                            highlight.OutlineTransparency = 0
                            highlight.Parent = _G.PulseESP.ESPFolder
                            
                            local billboard, nameLabel, distLabel = createBillboardLabel(objName, _G.PulseESP.espColor)
                            billboard.Adornee = obj.PrimaryPart
                            billboard.Parent = _G.PulseESP.ESPFolder
                            
                            _G.PulseESP.itemHighlights[obj] = {
                                highlight = highlight,
                                billboard = billboard,
                                nameLabel = nameLabel,
                                distLabel = distLabel
                            }
                        end
                        
                        if _G.PulseESP.itemHighlights[obj].distLabel then
                            _G.PulseESP.itemHighlights[obj].distLabel.Text = "[" .. math.floor(distance) .. "m]"
                        end
                    end
                end
            end
        end
    end)
    
    for obj, data in pairs(_G.PulseESP.itemHighlights) do
        if not currentItems[obj] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.itemHighlights[obj] = nil
        end
    end
end

local function updatePlayerESP()
    if not _G.PulseESP.playerESPActive then return end
    
    local localPlayer = game.Players.LocalPlayer
    if not localPlayer.Character or not localPlayer.Character:FindFirstChild("HumanoidRootPart") then return end
    
    local playerPos = localPlayer.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentPlayers = {}
    
    for _, player in ipairs(game.Players:GetPlayers()) do
        if player ~= localPlayer and player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
            local distance = (player.Character.HumanoidRootPart.Position - playerPos).Magnitude
            
            if distance <= maxDistance then
                currentPlayers[player] = true
                
                if not _G.PulseESP.playerHighlights[player] then
                    local highlight = Instance.new("Highlight")
                    highlight.Adornee = player.Character
                    highlight.FillColor = Color3.fromRGB(255, 100, 100)
                    highlight.OutlineColor = Color3.fromRGB(255, 100, 100)
                    highlight.FillTransparency = 0.5
                    highlight.OutlineTransparency = 0
                    highlight.Parent = _G.PulseESP.ESPFolder
                    
                    local billboard, nameLabel, distLabel = createBillboardLabel(player.Name, Color3.fromRGB(255, 100, 100))
                    billboard.Adornee = player.Character.HumanoidRootPart
                    billboard.Parent = _G.PulseESP.ESPFolder
                    
                    _G.PulseESP.playerHighlights[player] = {
                        highlight = highlight,
                        billboard = billboard,
                        nameLabel = nameLabel,
                        distLabel = distLabel
                    }
                end
                
                if _G.PulseESP.playerHighlights[player].distLabel then
                    _G.PulseESP.playerHighlights[player].distLabel.Text = "[" .. math.floor(distance) .. "m]"
                end
            end
        end
    end
    
    for player, data in pairs(_G.PulseESP.playerHighlights) do
        if not currentPlayers[player] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.playerHighlights[player] = nil
        end
    end
end

local function updateMonsterESP()
    if not _G.PulseESP.monsterESPActive then return end
    
    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end
    
    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentMonsters = {}
    
    local monsterNames = {"Monster", "Zombie", "Enemy", "Boss", "Creature", "Beast"}
    
    for _, folder in ipairs(workspace:GetChildren()) do
        if folder:IsA("Folder") or folder:IsA("Model") then
            for _, obj in ipairs(folder:GetChildren()) do
                if obj:IsA("Model") and obj.PrimaryPart then
                    local objName = obj.Name:lower()
                    local isMonster = false
                    
                    for _, monsterName in ipairs(monsterNames) do
                        if objName:find(monsterName:lower()) then
                            isMonster = true
                            break
                        end
                    end
                    
                    if isMonster then
                        local distance = (obj.PrimaryPart.Position - playerPos).Magnitude
                        
                        if distance <= maxDistance then
                            currentMonsters[obj] = true
                            
                            if not _G.PulseESP.monsterHighlights[obj] then
                                local highlight = Instance.new("Highlight")
                                highlight.Adornee = obj
                                highlight.FillColor = Color3.fromRGB(255, 0, 0)
                                highlight.OutlineColor = Color3.fromRGB(255, 0, 0)
                                highlight.FillTransparency = 0.4
                                highlight.OutlineTransparency = 0
                                highlight.Parent = _G.PulseESP.ESPFolder
                                
                                local billboard, nameLabel, distLabel = createBillboardLabel(obj.Name, Color3.fromRGB(255, 0, 0))
                                billboard.Adornee = obj.PrimaryPart
                                billboard.Parent = _G.PulseESP.ESPFolder
                                
                                _G.PulseESP.monsterHighlights[obj] = {
                                    highlight = highlight,
                                    billboard = billboard,
                                    nameLabel = nameLabel,
                                    distLabel = distLabel
                                }
                            end
                            
                            if _G.PulseESP.monsterHighlights[obj].distLabel then
                                _G.PulseESP.monsterHighlights[obj].distLabel.Text = "[" .. math.floor(distance) .. "m]"
                            end
                        end
                    end
                end
            end
        end
    end
    
    for obj, data in pairs(_G.PulseESP.monsterHighlights) do
        if not currentMonsters[obj] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.monsterHighlights[obj] = nil
        end
    end
end

local function updateCharacterESP()
    if not _G.PulseESP.characterESPActive then return end

    local player = game.Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then return end

    local playerPos = player.Character.HumanoidRootPart.Position
    local maxDistance = _G.PulseESP.renderDistance
    local currentCharacters = {}

    local charactersFolder = workspace:FindFirstChild("Characters")
    if not charactersFolder then return end

    pcall(function()
        for _, group in ipairs(charactersFolder:GetChildren()) do
            if group:IsA("Folder") or group:IsA("Model") then
                for _, character in ipairs(group:GetChildren()) do
                    if character:IsA("Model") and character.PrimaryPart then
                        local distance = (character.PrimaryPart.Position - playerPos).Magnitude

                        if distance <= maxDistance then
                            currentCharacters[character] = true

                            if not _G.PulseESP.characterHighlights[character] then
                                local highlight = Instance.new("Highlight")
                                highlight.Adornee = character
                                highlight.FillColor = Color3.fromRGB(255, 255, 0)
                                highlight.OutlineColor = Color3.fromRGB(255, 255, 0)
                                highlight.FillTransparency = 0.5
                                highlight.OutlineTransparency = 0
                                highlight.Parent = _G.PulseESP.ESPFolder

                                local billboard, nameLabel, distLabel = createBillboardLabel(character.Name, Color3.fromRGB(255, 255, 0))
                                billboard.Adornee = character.PrimaryPart
                                billboard.Parent = _G.PulseESP.ESPFolder

                                _G.PulseESP.characterHighlights[character] = {
                                    highlight = highlight,
                                    billboard = billboard,
                                    nameLabel = nameLabel,
                                    distLabel = distLabel
                                }
                            end

                            if _G.PulseESP.characterHighlights[character].distLabel then
                                _G.PulseESP.characterHighlights[character].distLabel.Text = "[" .. math.floor(distance) .. "m]"
                            end
                        end
                    end
                end
            end
        end
    end)

    for character, data in pairs(_G.PulseESP.characterHighlights) do
        if not currentCharacters[character] then
            pcall(function()
                if data.highlight then data.highlight:Destroy() end
                if data.billboard then data.billboard:Destroy() end
            end)
            _G.PulseESP.characterHighlights[character] = nil
        end
    end
end

local itemSelector = ESPTab:Dropdown({
    Title = "Item ESP Select",
    Desc = "Choose which item to highlight",
    Values = scanAvailableItems(),
    Value = "ALL",
    Callback = function(option)
        _G.PulseESP.selectedItem = tostring(option)
        if _G.PulseESP.itemESPActive then
            updateItemESP()
        end
    end
})

local itemESP = ESPTab:Toggle({
    Title = "Item ESP",
    Desc = "Highlight items with distance labels",
    Icon = "package",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.itemESPActive = state
        
        if state then
            _G.PulseESP.availableItems = scanAvailableItems()
            itemSelector:Set({
                Values = _G.PulseESP.availableItems,
                Value = _G.PulseESP.selectedItem
            })
            
            startESPThread("ItemESP", function()
                while _G.PulseESP.itemESPActive do
                    pcall(updateItemESP)
                    task.wait(3)
                end
            end)
        else
            if _G.PulseESP.activeThreads["ItemESP"] then
                task.cancel(_G.PulseESP.activeThreads["ItemESP"])
                _G.PulseESP.activeThreads["ItemESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.itemHighlights)
        end
    end
})

local monsterESP = ESPTab:Toggle({
    Title = "Monster ESP",
    Desc = "Highlight monsters and enemies",
    Icon = "skull",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.monsterESPActive = state

        if state then
            startESPThread("MonsterESP", function()
                while _G.PulseESP.monsterESPActive do
                    pcall(updateMonsterESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["MonsterESP"] then
                task.cancel(_G.PulseESP.activeThreads["MonsterESP"])
                _G.PulseESP.activeThreads["MonsterESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.monsterHighlights)
        end
    end
})

local characterESP = ESPTab:Toggle({
    Title = "Character ESP",
    Desc = "Highlight all character groups in workspace.Characters",
    Icon = "users",
    Type = "Checkbox",
    Default = false,
    Callback = function(state)
        _G.PulseESP.characterESPActive = state

        if state then
            startESPThread("CharacterESP", function()
                while _G.PulseESP.characterESPActive do
                    pcall(updateCharacterESP)
                    task.wait(2)
                end
            end)
        else
            if _G.PulseESP.activeThreads["CharacterESP"] then
                task.cancel(_G.PulseESP.activeThreads["CharacterESP"])
                _G.PulseESP.activeThreads["CharacterESP"] = nil
            end
            clearESPHighlights(_G.PulseESP.characterHighlights)
        end
    end
})

local renderDistanceSlider = ESPTab:Slider({
    Title = "ESP Distance",
    Desc = "Maximum ESP render distance",
    Step = 50,
    Value = {
        Min = 100,
        Max = 2000,
        Default = 500,
    },
    Callback = function(value)
        _G.PulseESP.renderDistance = tonumber(value) or 500
    end
})

local espColor = ESPTab:Colorpicker({
    Title = "Item ESP Color",
    Desc = "Color for item highlights",
    Default = Color3.fromRGB(0, 255, 0),
    Transparency = 0,
    Callback = function(color)
        _G.PulseESP.espColor = color
    end
})

local refreshItemsButton = ESPTab:Button({
    Title = "Refresh Item List",
    Desc = "Update available items in dropdown",
    Callback = function()
        _G.PulseESP.availableItems = scanAvailableItems()
        itemSelector:Set({
            Values = _G.PulseESP.availableItems,
            Value = _G.PulseESP.selectedItem
        })
    end
})

local clearAllESP = ESPTab:Button({
    Title = "Clear All ESP",
    Desc = "Remove all ESP highlights",
    Callback = function()
        clearESPHighlights(_G.PulseESP.itemHighlights)
        clearESPHighlights(_G.PulseESP.playerHighlights)
        clearESPHighlights(_G.PulseESP.monsterHighlights)
        clearESPHighlights(_G.PulseESP.characterHighlights)
    end
})

WindUI:Popup({
    Title = "Loaded",
    Icon = "check-circle",
    Content = "Script loaded! Press INSERT to toggle.",
    Buttons = {
        {
            Title = "OK",
            Callback = function()
            end,
            Variant = "Primary",
        }
    }
})
